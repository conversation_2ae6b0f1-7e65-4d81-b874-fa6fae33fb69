# Generated by Django 5.2.4 on 2025-07-17 09:10

from django.db import migrations, models
import django.db.models.deletion
from django.conf import settings


class Migration(migrations.Migration):
    dependencies = [
        ("incubator", "0016_remove_templateperformancemetrics_template_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        # First, drop existing tables if they exist
        migrations.RunSQL(
            "DROP TABLE IF EXISTS template_usage_analytics;",
            reverse_sql="-- No reverse operation needed"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS template_performance_metrics;",
            reverse_sql="-- No reverse operation needed"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS template_section_analytics;",
            reverse_sql="-- No reverse operation needed"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS user_template_interaction;",
            reverse_sql="-- No reverse operation needed"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS template_recommendation;",
            reverse_sql="-- No reverse operation needed"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS template_ab_test;",
            reverse_sql="-- No reverse operation needed"
        ),

        # Recreate the template analytics models with correct structure
        migrations.CreateModel(
            name='TemplateUsageAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('viewed_at', models.DateTimeField(auto_now_add=True)),
                ('selected_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('completion_time', models.DurationField(blank=True, null=True)),
                ('rating', models.PositiveSmallIntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='Rating from 1-5 stars', null=True)),
                ('feedback_text', models.TextField(blank=True)),
                ('business_plan_published', models.BooleanField(default=False)),
                ('funding_received', models.BooleanField(default=False)),
                ('business_launched', models.BooleanField(default=False)),
                ('user_agent', models.TextField(blank=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('referrer', models.URLField(blank=True)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usage_analytics', to='incubator.businessplantemplate')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'template_usage_analytics',
                'indexes': [
                    models.Index(fields=['template', 'viewed_at'], name='template_us_templat_8f072f_idx'),
                    models.Index(fields=['user', 'viewed_at'], name='template_us_user_id_ad19f2_idx'),
                    models.Index(fields=['completed_at'], name='template_us_complet_38ffb3_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='TemplatePerformanceMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_views', models.IntegerField(default=0)),
                ('total_selections', models.IntegerField(default=0)),
                ('total_completions', models.IntegerField(default=0)),
                ('unique_users', models.IntegerField(default=0)),
                ('selection_rate', models.FloatField(default=0.0)),
                ('completion_rate', models.FloatField(default=0.0)),
                ('average_completion_time', models.DurationField(blank=True, null=True)),
                ('average_rating', models.FloatField(default=0.0)),
                ('total_ratings', models.IntegerField(default=0)),
                ('net_promoter_score', models.FloatField(default=0.0)),
                ('business_plans_published', models.IntegerField(default=0)),
                ('funding_success_rate', models.FloatField(default=0.0)),
                ('business_launch_rate', models.FloatField(default=0.0)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('template', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='performance_metrics', to='incubator.businessplantemplate')),
            ],
            options={
                'db_table': 'template_performance_metrics',
            },
        ),
        migrations.CreateModel(
            name='TemplateSectionAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('section_key', models.CharField(max_length=100)),
                ('section_title', models.CharField(max_length=200)),
                ('total_views', models.IntegerField(default=0)),
                ('total_completions', models.IntegerField(default=0)),
                ('average_time_spent', models.DurationField(blank=True, null=True)),
                ('completion_rate', models.FloatField(default=0.0)),
                ('ai_assistance_used', models.IntegerField(default=0)),
                ('content_regenerated', models.IntegerField(default=0)),
                ('section_customized', models.IntegerField(default=0)),
                ('average_content_length', models.IntegerField(default=0)),
                ('user_satisfaction_score', models.FloatField(default=0.0)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='section_analytics', to='incubator.businessplantemplate')),
            ],
            options={
                'db_table': 'template_section_analytics',
                'indexes': [
                    models.Index(fields=['template', 'completion_rate'], name='template_se_templat_8a9b2c_idx'),
                    models.Index(fields=['section_key', 'completion_rate'], name='template_se_section_7d4e5f_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='UserTemplateInteraction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(max_length=100)),
                ('action_type', models.CharField(choices=[('view', 'View Template'), ('select', 'Select Template'), ('start', 'Start Working'), ('save_section', 'Save Section'), ('complete_section', 'Complete Section'), ('use_ai', 'Use AI Assistance'), ('customize', 'Customize Section'), ('export', 'Export Business Plan'), ('share', 'Share Business Plan'), ('rate', 'Rate Template'), ('feedback', 'Provide Feedback')], max_length=50)),
                ('section_key', models.CharField(blank=True, max_length=100)),
                ('action_data', models.JSONField(blank=True, default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('duration', models.DurationField(blank=True, null=True)),
                ('device_type', models.CharField(blank=True, max_length=20)),
                ('browser', models.CharField(blank=True, max_length=50)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='incubator.businessplantemplate')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'user_template_interaction',
                'indexes': [
                    models.Index(fields=['user', 'timestamp'], name='user_templa_user_id_bd72f3_idx'),
                    models.Index(fields=['template', 'action_type'], name='user_templa_templat_ebb591_idx'),
                    models.Index(fields=['session_id'], name='user_templa_session_e9cb9d_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='TemplateRecommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recommendation_score', models.FloatField(default=0.0)),
                ('recommendation_reason', models.TextField(blank=True)),
                ('viewed', models.BooleanField(default=False)),
                ('selected', models.BooleanField(default=False)),
                ('dismissed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('viewed_at', models.DateTimeField(blank=True, null=True)),
                ('selected_at', models.DateTimeField(blank=True, null=True)),
                ('recommended_template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recommendations', to='incubator.businessplantemplate')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'template_recommendation',
                'indexes': [
                    models.Index(fields=['user', 'created_at'], name='template_re_user_id_8f9a1b_idx'),
                    models.Index(fields=['recommendation_score'], name='template_re_recomme_2c3d4e_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='TemplateABTest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('test_name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('traffic_split', models.FloatField(default=50.0, help_text='Percentage for variant (0-100)')),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('original_conversions', models.IntegerField(default=0)),
                ('variant_conversions', models.IntegerField(default=0)),
                ('original_views', models.IntegerField(default=0)),
                ('variant_views', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('original_template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ab_tests_original', to='incubator.businessplantemplate')),
                ('variant_template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ab_tests_variant', to='incubator.businessplantemplate')),
            ],
            options={
                'db_table': 'template_ab_test',
                'indexes': [
                    models.Index(fields=['is_active', 'start_date'], name='template_ab_is_acti_5f6a7b_idx'),
                    models.Index(fields=['test_name'], name='template_ab_test_na_8c9d0e_idx'),
                ],
            },
        ),
        # Add unique constraints
        migrations.AddConstraint(
            model_name='templatesectionanalytics',
            constraint=models.UniqueConstraint(fields=['template', 'section_key'], name='unique_template_section'),
        ),
        migrations.AddConstraint(
            model_name='templaterecommendation',
            constraint=models.UniqueConstraint(fields=['user', 'recommended_template'], name='unique_user_recommendation'),
        ),
    ]
