#!/usr/bin/env python
"""
Quick Start Script - Get the application running immediately
This script bypasses problematic AI apps and gets core functionality working
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    print("🚀 Quick Start - AI Incubator Platform")
    print("=" * 50)
    
    # Get the backend directory
    backend_dir = Path(__file__).resolve().parent
    os.chdir(backend_dir)
    
    print(f"📁 Working directory: {backend_dir}")
    
    # Step 1: Copy backup database
    print("\n1️⃣ Setting up database...")
    try:
        if Path("db_backup.sqlite3").exists():
            if Path("db.sqlite3").exists():
                Path("db.sqlite3").unlink()  # Remove existing
            Path("db_backup.sqlite3").replace("db.sqlite3")
            print("✅ Database restored from backup")
        else:
            print("⚠️ No backup database found, using existing db.sqlite3")
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False
    
    # Step 2: Check virtual environment
    print("\n2️⃣ Checking virtual environment...")
    venv_python = Path("venv/Scripts/python.exe")
    if venv_python.exists():
        python_cmd = str(venv_python)
        print("✅ Using virtual environment Python")
    else:
        python_cmd = "python"
        print("⚠️ Virtual environment not found, using system Python")
    
    # Step 3: Test Django setup
    print("\n3️⃣ Testing Django configuration...")
    try:
        result = subprocess.run([
            python_cmd, "manage.py", "check", "--deploy"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Django configuration is valid")
        else:
            print(f"⚠️ Django check warnings: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("❌ Django check timed out - this indicates startup issues")
        return False
    except Exception as e:
        print(f"❌ Django check failed: {e}")
        return False
    
    # Step 4: Start the server
    print("\n4️⃣ Starting Django development server...")
    print("🌐 Server will be available at: http://localhost:8001")
    print("📝 Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Start server on port 8001 to avoid conflicts
        subprocess.run([
            python_cmd, "manage.py", "runserver", "8001"
        ], timeout=None)  # No timeout for server
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        return True
    except Exception as e:
        print(f"\n❌ Server failed to start: {e}")
        return False

def check_requirements():
    """Check if basic requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # Check if we're in the right directory
    if not Path("manage.py").exists():
        print("❌ manage.py not found - run this script from the backend directory")
        return False
    print("✅ Django project detected")
    
    return True

if __name__ == "__main__":
    if not check_requirements():
        sys.exit(1)
    
    success = main()
    sys.exit(0 if success else 1)
