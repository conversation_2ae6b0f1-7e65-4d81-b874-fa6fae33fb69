{"config": {"configFile": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/src/__tests__/e2e", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"test-type": "e2e", "test-scope": "admin-crud", "actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/playwright-junit.xml"}], ["line", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/test-results/playwright", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "test-scope": "admin-crud", "actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/test-results/playwright", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "test-scope": "admin-crud", "actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/test-results/playwright", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "test-scope": "admin-crud", "actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/test-results/playwright", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "test-scope": "admin-crud", "actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/test-results/playwright", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "test-scope": "admin-crud", "actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/test-results/playwright", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "test-scope": "admin-crud", "actualWorkers": 4}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/test-results/playwright", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "test-scope": "admin-crud", "actualWorkers": 4}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "admin-crud-workflow.test.ts", "file": "admin-crud-workflow.test.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Admin CRUD Workflows", "file": "admin-crud-workflow.test.ts", "line": 42, "column": 6, "specs": [], "suites": [{"title": "Business Plans Management", "file": "admin-crud-workflow.test.ts", "line": 47, "column": 8, "specs": [{"title": "should complete full CRUD workflow for business plans", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 24474, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"email-input\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"email-input\"]')\u001b[22m\n\n    at loginAsAdmin (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:16:14)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:44:5", "location": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts", "column": 14, "line": 16}, "snippet": "\u001b[0m \u001b[90m 14 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginAsAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m) {\n \u001b[90m 15 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 16 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_EMAIL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 17 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"password-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_PASSWORD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"login-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/admin/**'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts", "column": 14, "line": 16}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"email-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 14 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginAsAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m) {\n \u001b[90m 15 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 16 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_EMAIL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 17 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"password-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_PASSWORD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"login-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/admin/**'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginAsAdmin (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:16:14)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:44:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:33:48.075Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\test-results\\playwright\\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\test-results\\playwright\\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\test-results\\playwright\\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts", "column": 14, "line": 16}}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-8fad47aafa9218d5281b", "file": "admin-crud-workflow.test.ts", "line": 48, "column": 5}, {"title": "should handle search and filtering", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 18, "parallelIndex": 0, "status": "failed", "duration": 23721, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"email-input\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"email-input\"]')\u001b[22m\n\n    at loginAsAdmin (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:16:14)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:44:5", "location": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts", "column": 14, "line": 16}, "snippet": "\u001b[0m \u001b[90m 14 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginAsAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m) {\n \u001b[90m 15 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 16 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_EMAIL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 17 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"password-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_PASSWORD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"login-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/admin/**'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts", "column": 14, "line": 16}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"email-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 14 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginAsAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m) {\n \u001b[90m 15 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 16 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_EMAIL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 17 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"password-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_PASSWORD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"login-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/admin/**'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginAsAdmin (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:16:14)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:44:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:36.493Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\test-results\\playwright\\admin-crud-workflow-Admin--d85ae-handle-search-and-filtering-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\test-results\\playwright\\admin-crud-workflow-Admin--d85ae-handle-search-and-filtering-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\test-results\\playwright\\admin-crud-workflow-Admin--d85ae-handle-search-and-filtering-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts", "column": 14, "line": 16}}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-af02b9e99aadd2119a2e", "file": "admin-crud-workflow.test.ts", "line": 115, "column": 5}, {"title": "should complete full CRUD workflow for business plans", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 14, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:33:48.045Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-f1a34ca16755998bfe11", "file": "admin-crud-workflow.test.ts", "line": 48, "column": 5}, {"title": "should handle search and filtering", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 5, "parallelIndex": 2, "status": "failed", "duration": 38, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:33:52.362Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-625ef2fbe1355f9c18cd", "file": "admin-crud-workflow.test.ts", "line": 115, "column": 5}, {"title": "should complete full CRUD workflow for business plans", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 17, "parallelIndex": 2, "status": "failed", "duration": 22, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:32.777Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-9eeecc36924e6dbe7358", "file": "admin-crud-workflow.test.ts", "line": 48, "column": 5}, {"title": "should handle search and filtering", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "failed", "duration": 24, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:39.349Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-8fadcad592490a423e88", "file": "admin-crud-workflow.test.ts", "line": 115, "column": 5}, {"title": "should complete full CRUD workflow for business plans", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 31, "parallelIndex": 2, "status": "failed", "duration": 29151, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"email-input\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"email-input\"]')\u001b[22m\n\n    at loginAsAdmin (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:16:14)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:44:5", "location": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts", "column": 14, "line": 16}, "snippet": "\u001b[0m \u001b[90m 14 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginAsAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m) {\n \u001b[90m 15 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 16 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_EMAIL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 17 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"password-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_PASSWORD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"login-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/admin/**'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts", "column": 14, "line": 16}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"email-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 14 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginAsAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m) {\n \u001b[90m 15 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 16 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_EMAIL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 17 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"password-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_PASSWORD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"login-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/admin/**'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginAsAdmin (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:16:14)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:44:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:15.085Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\test-results\\playwright\\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\test-results\\playwright\\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-Mobile-Chrome\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts", "column": 14, "line": 16}}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-f857000e0e956dd18541", "file": "admin-crud-workflow.test.ts", "line": 48, "column": 5}, {"title": "should handle search and filtering", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-0ce1fbc465b8e4c763bb", "file": "admin-crud-workflow.test.ts", "line": 115, "column": 5}, {"title": "should complete full CRUD workflow for business plans", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-ef6150835d8896d25070", "file": "admin-crud-workflow.test.ts", "line": 48, "column": 5}, {"title": "should handle search and filtering", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-bb92f3b3126034f44e14", "file": "admin-crud-workflow.test.ts", "line": 115, "column": 5}, {"title": "should complete full CRUD workflow for business plans", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-9f9b9ed02a61f3bae7a1", "file": "admin-crud-workflow.test.ts", "line": 48, "column": 5}, {"title": "should handle search and filtering", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-9bcbaf88528fd5993c11", "file": "admin-crud-workflow.test.ts", "line": 115, "column": 5}, {"title": "should complete full CRUD workflow for business plans", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-48477107fb298810d0ad", "file": "admin-crud-workflow.test.ts", "line": 48, "column": 5}, {"title": "should handle search and filtering", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-ce1885f54cc809a9ff55", "file": "admin-crud-workflow.test.ts", "line": 115, "column": 5}]}, {"title": "Mentor Profiles Management", "file": "admin-crud-workflow.test.ts", "line": 134, "column": 8, "specs": [{"title": "should complete full CRUD workflow for mentor profiles", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 33, "parallelIndex": 0, "status": "failed", "duration": 25451, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"email-input\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"email-input\"]')\u001b[22m\n\n    at loginAsAdmin (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:16:14)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:44:5", "location": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts", "column": 14, "line": 16}, "snippet": "\u001b[0m \u001b[90m 14 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginAsAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m) {\n \u001b[90m 15 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 16 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_EMAIL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 17 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"password-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_PASSWORD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"login-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/admin/**'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts", "column": 14, "line": 16}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"email-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 14 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginAsAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m) {\n \u001b[90m 15 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 16 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_EMAIL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 17 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"password-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mADMIN_PASSWORD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"login-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/admin/**'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginAsAdmin (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:16:14)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts:44:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:18.265Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\test-results\\playwright\\admin-crud-workflow-Admin--5d620-orkflow-for-mentor-profiles-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\admin-crud-workflow.test.ts", "column": 14, "line": 16}}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-2eee0ba67c1a36a0b6d6", "file": "admin-crud-workflow.test.ts", "line": 135, "column": 5}, {"title": "should complete full CRUD workflow for mentor profiles", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 2, "status": "failed", "duration": 33, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:33:59.563Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-5a7efb02347c186c7747", "file": "admin-crud-workflow.test.ts", "line": 135, "column": 5}, {"title": "should complete full CRUD workflow for mentor profiles", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 21, "parallelIndex": 2, "status": "failed", "duration": 61, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:45.325Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-abcf6713296dedff0ed6", "file": "admin-crud-workflow.test.ts", "line": 135, "column": 5}, {"title": "should complete full CRUD workflow for mentor profiles", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-fa5589253417cd5d1690", "file": "admin-crud-workflow.test.ts", "line": 135, "column": 5}, {"title": "should complete full CRUD workflow for mentor profiles", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-9f38ce497b05e6f61e13", "file": "admin-crud-workflow.test.ts", "line": 135, "column": 5}, {"title": "should complete full CRUD workflow for mentor profiles", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-c3a61f66531b9d14a5dc", "file": "admin-crud-workflow.test.ts", "line": 135, "column": 5}, {"title": "should complete full CRUD workflow for mentor profiles", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-7777fc83d70aa694b8d1", "file": "admin-crud-workflow.test.ts", "line": 135, "column": 5}]}, {"title": "Funding Management", "file": "admin-crud-workflow.test.ts", "line": 197, "column": 8, "specs": [{"title": "should complete full CRUD workflow for funding opportunities", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-cc2bbbc29a41e044f541", "file": "admin-crud-workflow.test.ts", "line": 198, "column": 5}, {"title": "should complete full CRUD workflow for funding opportunities", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "failed", "duration": 32, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:07.820Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-5b5b26382bd98d7229bf", "file": "admin-crud-workflow.test.ts", "line": 198, "column": 5}, {"title": "should complete full CRUD workflow for funding opportunities", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 23, "parallelIndex": 2, "status": "failed", "duration": 21, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:53.751Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-7cace7149be6c2cac618", "file": "admin-crud-workflow.test.ts", "line": 198, "column": 5}, {"title": "should complete full CRUD workflow for funding opportunities", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-8dbb5c58593ecd32021c", "file": "admin-crud-workflow.test.ts", "line": 198, "column": 5}, {"title": "should complete full CRUD workflow for funding opportunities", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-bc1e93642274d31aff4e", "file": "admin-crud-workflow.test.ts", "line": 198, "column": 5}, {"title": "should complete full CRUD workflow for funding opportunities", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-05649f73c85eb0e77cd2", "file": "admin-crud-workflow.test.ts", "line": 198, "column": 5}, {"title": "should complete full CRUD workflow for funding opportunities", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-664e876df0cd06c489f8", "file": "admin-crud-workflow.test.ts", "line": 198, "column": 5}]}, {"title": "Milestones Management", "file": "admin-crud-workflow.test.ts", "line": 266, "column": 8, "specs": [{"title": "should complete full CRUD workflow for milestones", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-75f0b6bb8a44d44c4345", "file": "admin-crud-workflow.test.ts", "line": 267, "column": 5}, {"title": "should display analytics correctly", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-bbe4d9fc0d5ca48b0d75", "file": "admin-crud-workflow.test.ts", "line": 327, "column": 5}, {"title": "should complete full CRUD workflow for milestones", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 10, "parallelIndex": 2, "status": "failed", "duration": 26, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:13.976Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-2eddd52521a0ef6afed8", "file": "admin-crud-workflow.test.ts", "line": 267, "column": 5}, {"title": "should display analytics correctly", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 12, "parallelIndex": 2, "status": "failed", "duration": 26, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:20.008Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-6b291c45c5a9268f5e95", "file": "admin-crud-workflow.test.ts", "line": 327, "column": 5}, {"title": "should complete full CRUD workflow for milestones", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 25, "parallelIndex": 2, "status": "failed", "duration": 18, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:57.474Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-163f34a14beea26a7454", "file": "admin-crud-workflow.test.ts", "line": 267, "column": 5}, {"title": "should display analytics correctly", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 27, "parallelIndex": 2, "status": "failed", "duration": 68, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:02.868Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-50b172ae1d9e0c832845", "file": "admin-crud-workflow.test.ts", "line": 327, "column": 5}, {"title": "should complete full CRUD workflow for milestones", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-cae5e3a4f868a050a343", "file": "admin-crud-workflow.test.ts", "line": 267, "column": 5}, {"title": "should display analytics correctly", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-d75df446b7d505db9f9f", "file": "admin-crud-workflow.test.ts", "line": 327, "column": 5}, {"title": "should complete full CRUD workflow for milestones", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-56be8fa8acfefd8c9a10", "file": "admin-crud-workflow.test.ts", "line": 267, "column": 5}, {"title": "should display analytics correctly", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-ff7d2319b9dc69de27ae", "file": "admin-crud-workflow.test.ts", "line": 327, "column": 5}, {"title": "should complete full CRUD workflow for milestones", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-319982c95c2be6ac24fd", "file": "admin-crud-workflow.test.ts", "line": 267, "column": 5}, {"title": "should display analytics correctly", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-54bf06d267c09cd909cf", "file": "admin-crud-workflow.test.ts", "line": 327, "column": 5}, {"title": "should complete full CRUD workflow for milestones", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-026075c9c6cfc714a150", "file": "admin-crud-workflow.test.ts", "line": 267, "column": 5}, {"title": "should display analytics correctly", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-35051dfb8a80b8d2861d", "file": "admin-crud-workflow.test.ts", "line": 327, "column": 5}]}, {"title": "Cross-Entity Integration", "file": "admin-crud-workflow.test.ts", "line": 342, "column": 8, "specs": [{"title": "should handle relationships between entities", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-b5d0c6d87f161045ba98", "file": "admin-crud-workflow.test.ts", "line": 343, "column": 5}, {"title": "should handle relationships between entities", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 14, "parallelIndex": 2, "status": "failed", "duration": 31, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:27.444Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-c7a2a68adb8a2cfe6f2d", "file": "admin-crud-workflow.test.ts", "line": 343, "column": 5}, {"title": "should handle relationships between entities", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 29, "parallelIndex": 2, "status": "failed", "duration": 19, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:09.354Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "7d6e009931cad28dca27-ccc3847b71d868142baf", "file": "admin-crud-workflow.test.ts", "line": 343, "column": 5}, {"title": "should handle relationships between entities", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-1f6388f4a1438d0c3b1e", "file": "admin-crud-workflow.test.ts", "line": 343, "column": 5}, {"title": "should handle relationships between entities", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-32818d113206b12fdf31", "file": "admin-crud-workflow.test.ts", "line": 343, "column": 5}, {"title": "should handle relationships between entities", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-2b2db5aa2951c56e3580", "file": "admin-crud-workflow.test.ts", "line": 343, "column": 5}, {"title": "should handle relationships between entities", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "7d6e009931cad28dca27-fad26c7a56a6b906c24b", "file": "admin-crud-workflow.test.ts", "line": 343, "column": 5}]}]}]}, {"title": "redux-diagnostics.spec.ts", "file": "redux-diagnostics.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Redux Diagnostics Suite", "file": "redux-diagnostics.spec.ts", "line": 35, "column": 6, "specs": [{"title": "🔍 Redux Store Structure Validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 24397, "errors": [], "stdout": [{"text": "🧪 Testing Redux store structure...\n"}, {"text": "✅ Present slices: [\n  \u001b[32m'auth'\u001b[39m,      \u001b[32m'events'\u001b[39m,\n  \u001b[32m'admin'\u001b[39m,     \u001b[32m'language'\u001b[39m,\n  \u001b[32m'incubator'\u001b[39m, \u001b[32m'forum'\u001b[39m,\n  \u001b[32m'ai'\u001b[39m,        \u001b[32m'businessPlans'\u001b[39m,\n  \u001b[32m'aiContext'\u001b[39m, \u001b[32m'dashboard'\u001b[39m,\n  \u001b[32m'toast'\u001b[39m,     \u001b[32m'ui'\u001b[39m\n]\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:33:48.026Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "545a0295a0807e56a6cf-38758189ed6ba7b8709c", "file": "redux-diagnostics.spec.ts", "line": 49, "column": 3}, {"title": "🔐 Authentication State Consistency", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 12463, "errors": [], "stdout": [{"text": "🧪 Testing authentication state consistency...\n"}, {"text": "🔍 Auth State Analysis: { hasToken: \u001b[33mfalse\u001b[39m, isAuthenticated: \u001b[33mfalse\u001b[39m, hasUser: \u001b[33mfalse\u001b[39m, user: \u001b[1mnull\u001b[22m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:14.077Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "545a0295a0807e56a6cf-75016d8881c144c73f86", "file": "redux-diagnostics.spec.ts", "line": 94, "column": 3}, {"title": "🔄 Middleware Functionality Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 12404, "errors": [], "stdout": [{"text": "🧪 Testing Redux middleware...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:26.573Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "545a0295a0807e56a6cf-40e24c2e378f6848cdaa", "file": "redux-diagnostics.spec.ts", "line": 130, "column": 3}, {"title": "🚫 Context API Remnants Detection", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 15337, "errors": [], "stdout": [{"text": "🧪 Checking for Context API remnants...\n"}, {"text": "🔍 Context providers found: []\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:38.992Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "545a0295a0807e56a6cf-1ded375ae92080063e00", "file": "redux-diagnostics.spec.ts", "line": 163, "column": 3}, {"title": "📊 State Shape Validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 11578, "errors": [], "stdout": [{"text": "🧪 Validating Redux state shapes...\n"}, {"text": "✅ All state shapes are valid\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:54.348Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "545a0295a0807e56a6cf-53c61c8788f27c198358", "file": "redux-diagnostics.spec.ts", "line": 194, "column": 3}, {"title": "🔄 Action Dispatch and State Updates", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 11452, "errors": [], "stdout": [{"text": "🧪 Testing action dispatch and state updates...\n"}, {"text": "✅ Action dispatch working correctly\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:05.943Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "545a0295a0807e56a6cf-9f91a48cb5cb9a5f2961", "file": "redux-diagnostics.spec.ts", "line": 226, "column": 3}, {"title": "🌐 Language State Integration", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 31852, "error": {"message": "TimeoutError: page.waitForFunction: Timeout 10000ms exceeded.", "stack": "TimeoutError: page.waitForFunction: Timeout 10000ms exceeded.\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\redux-diagnostics.spec.ts:46:16", "location": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\redux-diagnostics.spec.ts", "column": 16, "line": 46}, "snippet": "\u001b[0m \u001b[90m 44 |\u001b[39m     \n \u001b[90m 45 |\u001b[39m     \u001b[90m// Wait for Redux store to be available\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 46 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForFunction(() \u001b[33m=>\u001b[39m window\u001b[33m.\u001b[39m__REDUX_STORE__ \u001b[33m||\u001b[39m window\u001b[33m.\u001b[39mstore)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 47 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m   test(\u001b[32m'🔍 Redux Store Structure Validation'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\redux-diagnostics.spec.ts", "column": 16, "line": 46}, "message": "TimeoutError: page.waitForFunction: Timeout 10000ms exceeded.\n\n\u001b[0m \u001b[90m 44 |\u001b[39m     \n \u001b[90m 45 |\u001b[39m     \u001b[90m// Wait for Redux store to be available\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 46 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForFunction(() \u001b[33m=>\u001b[39m window\u001b[33m.\u001b[39m__REDUX_STORE__ \u001b[33m||\u001b[39m window\u001b[33m.\u001b[39mstore)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 47 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m   test(\u001b[32m'🔍 Redux Store Structure Validation'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\redux-diagnostics.spec.ts:46:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:17.408Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\test-results\\playwright\\redux-diagnostics-Redux-Di-d7d6d--Language-State-Integration-chromium\\test-failed-1.png"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\__tests__\\e2e\\redux-diagnostics.spec.ts", "column": 16, "line": 46}}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-906a6602146bd034cdad", "file": "redux-diagnostics.spec.ts", "line": 254, "column": 3}, {"title": "🚨 Error Handling and Recovery", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-fb690201f47c5152631c", "file": "redux-diagnostics.spec.ts", "line": 285, "column": 3}, {"title": "🔗 Token and Authentication Flow", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-44f1cd39be6ff8b3b81c", "file": "redux-diagnostics.spec.ts", "line": 320, "column": 3}, {"title": "🎯 Redux DevTools Integration", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-694406d370d76ba703ff", "file": "redux-diagnostics.spec.ts", "line": 345, "column": 3}, {"title": "📱 Responsive State Management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-a6d78f1aa4749a85928d", "file": "redux-diagnostics.spec.ts", "line": 361, "column": 3}, {"title": "🔍 Redux Store Structure Validation", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 21, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:33:48.001Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-3c707e65a73235a21a5b", "file": "redux-diagnostics.spec.ts", "line": 49, "column": 3}, {"title": "🔐 Authentication State Consistency", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 3, "status": "failed", "duration": 11, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:33:52.244Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-2031c78025f416df6c30", "file": "redux-diagnostics.spec.ts", "line": 94, "column": 3}, {"title": "🔄 Middleware Functionality Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 3, "status": "failed", "duration": 102, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:01.013Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-c500a807b2935cb4d96d", "file": "redux-diagnostics.spec.ts", "line": 130, "column": 3}, {"title": "🚫 Context API Remnants Detection", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 8, "parallelIndex": 3, "status": "failed", "duration": 50, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:08.405Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-af9da96c274db62345c4", "file": "redux-diagnostics.spec.ts", "line": 163, "column": 3}, {"title": "📊 State Shape Validation", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "failed", "duration": 25, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:15.000Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-35710a7b60aceef32753", "file": "redux-diagnostics.spec.ts", "line": 194, "column": 3}, {"title": "🔄 Action Dispatch and State Updates", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 13, "parallelIndex": 3, "status": "failed", "duration": 31, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:20.165Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-43066fe0024cb0021b5c", "file": "redux-diagnostics.spec.ts", "line": 226, "column": 3}, {"title": "🌐 Language State Integration", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 15, "parallelIndex": 3, "status": "failed", "duration": 26, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:27.249Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-47d43ddf835f1c8575f3", "file": "redux-diagnostics.spec.ts", "line": 254, "column": 3}, {"title": "🚨 Error Handling and Recovery", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 16, "parallelIndex": 3, "status": "failed", "duration": 57, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:33.279Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-423befad575729b542de", "file": "redux-diagnostics.spec.ts", "line": 285, "column": 3}, {"title": "🔗 Token and Authentication Flow", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "failed", "duration": 30, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:39.751Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-7a69dca918a92f9500ff", "file": "redux-diagnostics.spec.ts", "line": 320, "column": 3}, {"title": "🎯 Redux DevTools Integration", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 22, "parallelIndex": 3, "status": "failed", "duration": 128, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:45.747Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-4e4dd231464c766d46a2", "file": "redux-diagnostics.spec.ts", "line": 345, "column": 3}, {"title": "📱 Responsive State Management", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 24, "parallelIndex": 3, "status": "failed", "duration": 20, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1489\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:53.817Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-efa6372f263579f44732", "file": "redux-diagnostics.spec.ts", "line": 361, "column": 3}, {"title": "🔍 Redux Store Structure Validation", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 26, "parallelIndex": 3, "status": "failed", "duration": 19, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:34:57.617Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-d39116644be1686c2bcd", "file": "redux-diagnostics.spec.ts", "line": 49, "column": 3}, {"title": "🔐 Authentication State Consistency", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 28, "parallelIndex": 3, "status": "failed", "duration": 66, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:03.207Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-35551d9bd659b00b6c19", "file": "redux-diagnostics.spec.ts", "line": 94, "column": 3}, {"title": "🔄 Middleware Functionality Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 30, "parallelIndex": 3, "status": "failed", "duration": 19, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:09.625Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-92d6e55f758e09fbbc7c", "file": "redux-diagnostics.spec.ts", "line": 130, "column": 3}, {"title": "🚫 Context API Remnants Detection", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 32, "parallelIndex": 3, "status": "failed", "duration": 68, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:15.169Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-0f6126f0c2b56d61f951", "file": "redux-diagnostics.spec.ts", "line": 163, "column": 3}, {"title": "📊 State Shape Validation", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 34, "parallelIndex": 3, "status": "failed", "duration": 42, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:28.817Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-adff974b7bd9d478a9a9", "file": "redux-diagnostics.spec.ts", "line": 194, "column": 3}, {"title": "🔄 Action Dispatch and State Updates", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 35, "parallelIndex": 3, "status": "failed", "duration": 34, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:35.637Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-562850c0d68815535102", "file": "redux-diagnostics.spec.ts", "line": 226, "column": 3}, {"title": "🌐 Language State Integration", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 36, "parallelIndex": 3, "status": "failed", "duration": 37, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2191\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:45.144Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "545a0295a0807e56a6cf-b49c95ec3626d9f68a67", "file": "redux-diagnostics.spec.ts", "line": 254, "column": 3}, {"title": "🚨 Error Handling and Recovery", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 37, "parallelIndex": 3, "status": "skipped", "duration": 3, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-16T17:35:49.751Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "545a0295a0807e56a6cf-567af6521b509aabcf26", "file": "redux-diagnostics.spec.ts", "line": 285, "column": 3}, {"title": "🔗 Token and Authentication Flow", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-cf1587df072fe4d7820e", "file": "redux-diagnostics.spec.ts", "line": 320, "column": 3}, {"title": "🎯 Redux DevTools Integration", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-79b17e8c0835935f578b", "file": "redux-diagnostics.spec.ts", "line": 345, "column": 3}, {"title": "📱 Responsive State Management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-3d69e7eb51d7d620cbae", "file": "redux-diagnostics.spec.ts", "line": 361, "column": 3}, {"title": "🔍 Redux Store Structure Validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-b8a5c9df82b2e5a51aec", "file": "redux-diagnostics.spec.ts", "line": 49, "column": 3}, {"title": "🔐 Authentication State Consistency", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-1fb20da9e6f700d1ba92", "file": "redux-diagnostics.spec.ts", "line": 94, "column": 3}, {"title": "🔄 Middleware Functionality Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-6332b61e012fe5295eeb", "file": "redux-diagnostics.spec.ts", "line": 130, "column": 3}, {"title": "🚫 Context API Remnants Detection", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-066a97c721a413e70caa", "file": "redux-diagnostics.spec.ts", "line": 163, "column": 3}, {"title": "📊 State Shape Validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-4cb9f7c33d42e1d8030f", "file": "redux-diagnostics.spec.ts", "line": 194, "column": 3}, {"title": "🔄 Action Dispatch and State Updates", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-2010c45987053974750f", "file": "redux-diagnostics.spec.ts", "line": 226, "column": 3}, {"title": "🌐 Language State Integration", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-168e6746dd53fc3c9477", "file": "redux-diagnostics.spec.ts", "line": 254, "column": 3}, {"title": "🚨 Error Handling and Recovery", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-069be5e7cd65d5e742cd", "file": "redux-diagnostics.spec.ts", "line": 285, "column": 3}, {"title": "🔗 Token and Authentication Flow", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-ac9471a8828717ab67f8", "file": "redux-diagnostics.spec.ts", "line": 320, "column": 3}, {"title": "🎯 Redux DevTools Integration", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-d5d61f0f4820ee3c97e6", "file": "redux-diagnostics.spec.ts", "line": 345, "column": 3}, {"title": "📱 Responsive State Management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-db6afdfa3076eb200771", "file": "redux-diagnostics.spec.ts", "line": 361, "column": 3}, {"title": "🔍 Redux Store Structure Validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-0a87ed849834eff4acb6", "file": "redux-diagnostics.spec.ts", "line": 49, "column": 3}, {"title": "🔐 Authentication State Consistency", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-c208e87bf254b6002b35", "file": "redux-diagnostics.spec.ts", "line": 94, "column": 3}, {"title": "🔄 Middleware Functionality Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-2b6bea9f9737832b6d2e", "file": "redux-diagnostics.spec.ts", "line": 130, "column": 3}, {"title": "🚫 Context API Remnants Detection", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-b02b64a219cb522b186c", "file": "redux-diagnostics.spec.ts", "line": 163, "column": 3}, {"title": "📊 State Shape Validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-66b7503e9c8b0efaf11c", "file": "redux-diagnostics.spec.ts", "line": 194, "column": 3}, {"title": "🔄 Action Dispatch and State Updates", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-216dcd276fd032ee87f5", "file": "redux-diagnostics.spec.ts", "line": 226, "column": 3}, {"title": "🌐 Language State Integration", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-6f5111fe0cba5f12870a", "file": "redux-diagnostics.spec.ts", "line": 254, "column": 3}, {"title": "🚨 Error Handling and Recovery", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-79ed3f432455b59de422", "file": "redux-diagnostics.spec.ts", "line": 285, "column": 3}, {"title": "🔗 Token and Authentication Flow", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-4ae7e2eafd811353d04e", "file": "redux-diagnostics.spec.ts", "line": 320, "column": 3}, {"title": "🎯 Redux DevTools Integration", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-db615545f6c7429e4895", "file": "redux-diagnostics.spec.ts", "line": 345, "column": 3}, {"title": "📱 Responsive State Management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-8c4cb72c103af09686d0", "file": "redux-diagnostics.spec.ts", "line": 361, "column": 3}, {"title": "🔍 Redux Store Structure Validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-5f541b6d8efba83abec2", "file": "redux-diagnostics.spec.ts", "line": 49, "column": 3}, {"title": "🔐 Authentication State Consistency", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-07a883244127ae32d3b3", "file": "redux-diagnostics.spec.ts", "line": 94, "column": 3}, {"title": "🔄 Middleware Functionality Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-3f45eecc0900581e1711", "file": "redux-diagnostics.spec.ts", "line": 130, "column": 3}, {"title": "🚫 Context API Remnants Detection", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-b0c4ec28cb7c49c62182", "file": "redux-diagnostics.spec.ts", "line": 163, "column": 3}, {"title": "📊 State Shape Validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-bf6be2acfc087f7adb38", "file": "redux-diagnostics.spec.ts", "line": 194, "column": 3}, {"title": "🔄 Action Dispatch and State Updates", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-3f7fde4b93812b691002", "file": "redux-diagnostics.spec.ts", "line": 226, "column": 3}, {"title": "🌐 Language State Integration", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-b97cbc16f1ba7054a583", "file": "redux-diagnostics.spec.ts", "line": 254, "column": 3}, {"title": "🚨 Error Handling and Recovery", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-c8970cfca89b10105837", "file": "redux-diagnostics.spec.ts", "line": 285, "column": 3}, {"title": "🔗 Token and Authentication Flow", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-b03e6cd05aa84c78108d", "file": "redux-diagnostics.spec.ts", "line": 320, "column": 3}, {"title": "🎯 Redux DevTools Integration", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-25819a13b13caf32667a", "file": "redux-diagnostics.spec.ts", "line": 345, "column": 3}, {"title": "📱 Responsive State Management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-0b1bf1174c96be6a357b", "file": "redux-diagnostics.spec.ts", "line": 361, "column": 3}, {"title": "🔍 Redux Store Structure Validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-fff739c4cbfa422dfee8", "file": "redux-diagnostics.spec.ts", "line": 49, "column": 3}, {"title": "🔐 Authentication State Consistency", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-e58d41d7c5c66124ab55", "file": "redux-diagnostics.spec.ts", "line": 94, "column": 3}, {"title": "🔄 Middleware Functionality Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-56ef8ed479f8c3fb65be", "file": "redux-diagnostics.spec.ts", "line": 130, "column": 3}, {"title": "🚫 Context API Remnants Detection", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-d9377f3a97ecb8224a98", "file": "redux-diagnostics.spec.ts", "line": 163, "column": 3}, {"title": "📊 State Shape Validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-5211b6204c1738782b6c", "file": "redux-diagnostics.spec.ts", "line": 194, "column": 3}, {"title": "🔄 Action Dispatch and State Updates", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-40c19bb7c127a287b463", "file": "redux-diagnostics.spec.ts", "line": 226, "column": 3}, {"title": "🌐 Language State Integration", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-23b0687b0e0672923bf0", "file": "redux-diagnostics.spec.ts", "line": 254, "column": 3}, {"title": "🚨 Error Handling and Recovery", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-fcf8592ec21283ffdc80", "file": "redux-diagnostics.spec.ts", "line": 285, "column": 3}, {"title": "🔗 Token and Authentication Flow", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-12b891ddb29ab1053801", "file": "redux-diagnostics.spec.ts", "line": 320, "column": 3}, {"title": "🎯 Redux DevTools Integration", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-d3e8c2f169bb9659208b", "file": "redux-diagnostics.spec.ts", "line": 345, "column": 3}, {"title": "📱 Responsive State Management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "545a0295a0807e56a6cf-a10c27c45029dce1efca", "file": "redux-diagnostics.spec.ts", "line": 361, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-16T17:33:44.677Z", "duration": 131069.744, "expected": 6, "skipped": 83, "unexpected": 37, "flaky": 0}}