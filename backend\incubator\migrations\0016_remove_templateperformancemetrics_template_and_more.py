# Generated by Django 5.2.4 on 2025-07-17 08:56

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("incubator", "0015_auto_20250717_1130"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="templateperformancemetrics",
            name="template",
        ),
        migrations.AlterUniqueTogether(
            name="templaterecommendation",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="templaterecommendation",
            name="recommended_template",
        ),
        migrations.RemoveField(
            model_name="templaterecommendation",
            name="user",
        ),
        migrations.AlterUniqueTogether(
            name="templatesectionanalytics",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="templatesectionanalytics",
            name="template",
        ),
        migrations.RemoveField(
            model_name="templateusageanalytics",
            name="template",
        ),
        migrations.RemoveField(
            model_name="templateusageanalytics",
            name="user",
        ),
        migrations.Remo<PERSON><PERSON>ield(
            model_name="usertemplateinteraction",
            name="template",
        ),
        migrations.Remove<PERSON>ield(
            model_name="usertemplateinteraction",
            name="user",
        ),
        migrations.DeleteModel(
            name="TemplateABTest",
        ),
        migrations.DeleteModel(
            name="TemplatePerformanceMetrics",
        ),
        migrations.DeleteModel(
            name="TemplateRecommendation",
        ),
        migrations.DeleteModel(
            name="TemplateSectionAnalytics",
        ),
        migrations.DeleteModel(
            name="TemplateUsageAnalytics",
        ),
        migrations.DeleteModel(
            name="UserTemplateInteraction",
        ),
    ]
