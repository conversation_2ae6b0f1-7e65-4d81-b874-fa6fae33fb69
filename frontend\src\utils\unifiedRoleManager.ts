/**
 * Unified Role Manager
 * Centralized role management system for user permissions and access control
 */

import { User } from '../services/api';

export interface RoleManager {
  getUserRoles(): string[];
  getPrimaryRole(): string;
  hasRole(role: string): boolean;
  hasAnyRole(roles: string[]): boolean;
  hasPermission(permission: string): boolean;
  getPermissionLevel(): string;
  isAdmin(): boolean;
  isSuperAdmin(): boolean;
  isModerator(): boolean;
  isInvestor(): boolean;
  isMentor(): boolean;
  canAccess(resource: string): boolean;
}

/**
 * Create a role manager instance for a user
 */
export function createRoleManager(user: User | null): RoleManager {
  const getUserRoles = (): string[] => {
    if (!user) return ['guest'];
    
    // Extract roles from user object
    const roles: string[] = [];
    
    // Check if user has roles array
    if (user.roles && Array.isArray(user.roles)) {
      roles.push(...user.roles.map(role => typeof role === 'string' ? role : role.name));
    }
    
    // Check if user has a single role
    if (user.role) {
      roles.push(typeof user.role === 'string' ? user.role : user.role.name);
    }
    
    // Check user profile roles
    if (user.profile?.roles && Array.isArray(user.profile.roles)) {
      roles.push(...user.profile.roles.map(role => typeof role === 'string' ? role : role.name));
    }
    
    // Check admin flags
    if (user.is_superuser || user.is_super_admin) {
      roles.push('super_admin');
    }
    
    if (user.is_staff || user.is_admin) {
      roles.push('admin');
    }
    
    // Default role if no roles found
    if (roles.length === 0) {
      roles.push('user');
    }
    
    // Remove duplicates and return
    return [...new Set(roles)];
  };

  const getPrimaryRole = (): string => {
    const roles = getUserRoles();
    
    // Role hierarchy (highest to lowest)
    const roleHierarchy = [
      'super_admin',
      'admin', 
      'moderator',
      'investor',
      'mentor',
      'user',
      'guest'
    ];
    
    // Return the highest role in hierarchy
    for (const role of roleHierarchy) {
      if (roles.includes(role)) {
        return role;
      }
    }
    
    return 'user';
  };

  const hasRole = (role: string): boolean => {
    return getUserRoles().includes(role);
  };

  const hasAnyRole = (roles: string[]): boolean => {
    const userRoles = getUserRoles();
    return roles.some(role => userRoles.includes(role));
  };

  const hasPermission = (permission: string): boolean => {
    const roles = getUserRoles();
    
    // Define permissions by role
    const rolePermissions: Record<string, string[]> = {
      super_admin: ['*'], // All permissions
      admin: [
        'manage_users', 'manage_content', 'manage_system', 
        'view_analytics', 'moderate_content', 'manage_roles'
      ],
      moderator: [
        'moderate_content', 'view_reports', 'manage_posts', 'manage_comments'
      ],
      investor: [
        'view_business_ideas', 'invest', 'view_analytics', 'manage_portfolio'
      ],
      mentor: [
        'view_business_ideas', 'provide_mentorship', 'view_mentee_progress'
      ],
      user: [
        'create_business_ideas', 'view_own_content', 'participate_forum'
      ],
      guest: ['view_public_content']
    };
    
    // Check if any user role has the permission
    return roles.some(role => {
      const permissions = rolePermissions[role] || [];
      return permissions.includes('*') || permissions.includes(permission);
    });
  };

  const getPermissionLevel = (): string => {
    const primaryRole = getPrimaryRole();
    
    const permissionLevels: Record<string, string> = {
      super_admin: 'super_admin',
      admin: 'admin',
      moderator: 'moderate',
      investor: 'write',
      mentor: 'write',
      user: 'write',
      guest: 'read'
    };
    
    return permissionLevels[primaryRole] || 'read';
  };

  const isAdmin = (): boolean => {
    return hasAnyRole(['admin', 'super_admin']);
  };

  const isSuperAdmin = (): boolean => {
    return hasRole('super_admin');
  };

  const isModerator = (): boolean => {
    return hasRole('moderator');
  };

  const isInvestor = (): boolean => {
    return hasRole('investor');
  };

  const isMentor = (): boolean => {
    return hasRole('mentor');
  };

  const canAccess = (resource: string): boolean => {
    const roles = getUserRoles();
    
    // Define resource access by role
    const resourceAccess: Record<string, string[]> = {
      'admin_panel': ['admin', 'super_admin'],
      'super_admin_panel': ['super_admin'],
      'moderation_tools': ['moderator', 'admin', 'super_admin'],
      'investment_tools': ['investor', 'admin', 'super_admin'],
      'mentorship_tools': ['mentor', 'admin', 'super_admin'],
      'ai_advanced': ['admin', 'super_admin'],
      'ai_basic': ['user', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'],
      'analytics': ['admin', 'super_admin', 'investor'],
      'system_logs': ['super_admin'],
      'user_management': ['admin', 'super_admin']
    };
    
    const allowedRoles = resourceAccess[resource] || [];
    return roles.some(role => allowedRoles.includes(role));
  };

  return {
    getUserRoles,
    getPrimaryRole,
    hasRole,
    hasAnyRole,
    hasPermission,
    getPermissionLevel,
    isAdmin,
    isSuperAdmin,
    isModerator,
    isInvestor,
    isMentor,
    canAccess
  };
}

/**
 * Helper function to get user roles directly
 */
export function getUserRoles(user: User | null): string[] {
  return createRoleManager(user).getUserRoles();
}

/**
 * Helper function to get primary user role
 */
export function getPrimaryUserRole(user: User | null): string {
  return createRoleManager(user).getPrimaryRole();
}

/**
 * Helper function to check if user has specific role
 */
export function userHasRole(user: User | null, role: string): boolean {
  return createRoleManager(user).hasRole(role);
}

/**
 * Helper function to check if user is admin
 */
export function isUserAdmin(user: User | null): boolean {
  return createRoleManager(user).isAdmin();
}

/**
 * Helper function to check if user is super admin
 */
export function isUserSuperAdmin(user: User | null): boolean {
  return createRoleManager(user).isSuperAdmin();
}

export default createRoleManager;
