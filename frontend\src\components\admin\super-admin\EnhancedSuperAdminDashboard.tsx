import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Shield, Database, Server, Users, Activity, AlertTriangle,
  CheckCircle, XCircle, RefreshCw, Download, Upload, Settings,
  BarChart3, Lock, Key, Monitor, HardDrive, Cpu, Network,
  FileText, Archive, Clock, Eye, Zap, Globe, Terminal,
  UserCheck, UserX, UserPlus, Search, Filter, ChevronDown,
  TrendingUp, TrendingDown, AlertCircle, Info
} from 'lucide-react';
import { getAuthToken } from '../../../services/api';
// DashboardLayout removed - handled by routing system with AuthenticatedLayout

interface SuperAdminCapability {
  id: string;
  name: string;
  description: string;
  features: string[];
  status: 'active' | 'inactive' | 'warning';
  usage_count?: number;
  last_used?: string;
}

interface SystemMetrics {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_io: number;
  active_connections: number;
  response_time: number;
}

interface SecurityAlert {
  id: string;
  type: 'warning' | 'critical' | 'info';
  message: string;
  timestamp: string;
  resolved: boolean;
}

const EnhancedSuperAdminDashboard: React.FC = () => {
  const { t } = useTranslation();
  const [capabilities, setCapabilities] = useState<SuperAdminCapability[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [securityAlerts, setSecurityAlerts] = useState<SecurityAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'system' | 'users' | 'security' | 'analytics'>('overview');

  useEffect(() => {
    fetchEnhancedData();
    const interval = setInterval(fetchEnhancedData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchEnhancedData = async () => {
    try {
      setLoading(true);
      
      // Fetch enhanced capabilities
      const capabilitiesResponse = await fetch('/api/users/super-admin/capabilities/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (capabilitiesResponse.ok) {
        const capabilitiesData = await capabilitiesResponse.json();
        setCapabilities(capabilitiesData.capabilities || []);
      }

      // Fetch system metrics
      const metricsResponse = await fetch('/api/superadmin/system/system_health/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setSystemMetrics(metricsData);
      } else {
        console.error('Failed to fetch system metrics:', metricsResponse.status);
        setSystemMetrics(null);
      }

      // Fetch security alerts from API
      const alertsResponse = await fetch('/api/superadmin/system/security/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (alertsResponse.ok) {
        const alertsData = await alertsResponse.json();
        setSecurityAlerts(alertsData.security_events || []);
      } else {
        console.error('Failed to fetch security alerts:', alertsResponse.status);
        setSecurityAlerts([]);
      }

    } catch (error) {
      console.error('Error fetching enhanced data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'inactive': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical': return <AlertTriangle className="w-5 h-5 text-red-400" />;
      case 'warning': return <AlertCircle className="w-5 h-5 text-yellow-400" />;
      case 'info': return <Info className="w-5 h-5 text-blue-400" />;
      default: return <Info className="w-5 h-5 text-gray-400" />;
    }
  };

  if (loading) {
    return (
      <DashboardLayout currentPage="enhanced-super-admin">
        <div className="flex items-center justify-center min-h-screen">
          <div className="flex items-center gap-3">
            <RefreshCw className="w-6 h-6 text-blue-400 animate-spin" />
            <span className="text-white">Loading Enhanced Super Admin Dashboard...</span>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-red-900 to-purple-900">
      <div className="px-4 sm:px-6 lg:px-8 py-6">
        <div className="max-w-7xl mx-auto text-white">
      {/* Enhanced Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <Shield className="w-10 h-10 text-red-400" />
              <div>
                <h1 className="text-3xl font-bold text-white">
                  {t('superAdmin.enhanced.title', 'Enhanced Super Admin Control Center')}
                </h1>
                <p className="text-gray-400">
                  {t('superAdmin.enhanced.subtitle', 'Ultimate system mastery and control')}
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg flex items-center gap-2">
              <RefreshCw className="w-4 h-4" />
              Refresh
            </button>
            <button className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export Report
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex gap-1 mt-6">
          {[
            { id: 'overview', label: 'Overview', icon: Monitor },
            { id: 'system', label: 'System Control', icon: Server },
            { id: 'users', label: 'User Management', icon: Users },
            { id: 'security', label: 'Security Center', icon: Lock },
            { id: 'analytics', label: 'Advanced Analytics', icon: BarChart3 }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Content Area */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* System Health Overview */}
            {systemMetrics && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gray-800 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <Cpu className="w-5 h-5 text-blue-400" />
                      <span className="font-medium">CPU Usage</span>
                    </div>
                    <span className={`text-sm ${systemMetrics.cpu_usage > 80 ? 'text-red-400' : 'text-green-400'}`}>
                      {systemMetrics.cpu_usage}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${systemMetrics.cpu_usage > 80 ? 'bg-red-500' : 'bg-blue-500'}`}
                      style={{ width: `${systemMetrics.cpu_usage}%` }}
                    />
                  </div>
                </div>

                <div className="glass-morphism rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <HardDrive className="w-5 h-5 text-green-400" />
                      <span className="font-medium text-glass-primary">Memory</span>
                    </div>
                    <span className={`text-sm ${systemMetrics.memory_usage > 85 ? 'text-red-400' : 'text-green-400'}`}>
                      {systemMetrics.memory_usage}%
                    </span>
                  </div>
                  <div className="w-full glass-light rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${systemMetrics.memory_usage > 85 ? 'bg-red-500' : 'bg-green-500'}`}
                      style={{ width: `${systemMetrics.memory_usage}%` }}
                    />
                  </div>
                </div>

                <div className="bg-gray-800 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <Network className="w-5 h-5 text-purple-400" />
                      <span className="font-medium">Network I/O</span>
                    </div>
                    <span className="text-sm text-purple-400">
                      {systemMetrics.network_io} MB/s
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Activity className="w-4 h-4 text-purple-400" />
                    <span className="text-sm text-gray-400">Active</span>
                  </div>
                </div>

                <div className="bg-gray-800 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <Globe className="w-5 h-5 text-yellow-400" />
                      <span className="font-medium">Connections</span>
                    </div>
                    <span className="text-sm text-yellow-400">
                      {systemMetrics.active_connections}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-sm text-gray-400">Healthy</span>
                  </div>
                </div>
              </div>
            )}

            {/* Security Alerts */}
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-400" />
                  Security Alerts
                </h3>
                <button className="text-blue-400 hover:text-blue-300 text-sm">
                  View All
                </button>
              </div>
              
              <div className="space-y-3">
                {securityAlerts.map(alert => (
                  <div key={alert.id} className={`flex items-center gap-3 p-3 rounded-lg ${
                    alert.resolved ? 'bg-gray-700/50' : 'bg-gray-700'
                  }`}>
                    {getAlertIcon(alert.type)}
                    <div className="flex-1">
                      <p className={`text-sm ${alert.resolved ? 'text-gray-400' : 'text-white'}`}>
                        {alert.message}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(alert.timestamp).toLocaleString()}
                      </p>
                    </div>
                    {alert.resolved && (
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Enhanced Capabilities Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {capabilities.map(capability => (
                <div key={capability.id} className="bg-gray-800 rounded-lg p-6 hover:bg-gray-750 transition-colors">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold text-lg">{capability.name}</h3>
                    <div className={`w-3 h-3 rounded-full ${
                      capability.status === 'active' ? 'bg-green-400' :
                      capability.status === 'warning' ? 'bg-yellow-400' : 'bg-red-400'
                    }`} />
                  </div>
                  
                  <p className="text-gray-400 text-sm mb-4">{capability.description}</p>
                  
                  <div className="space-y-2">
                    {capability.features.slice(0, 3).map((feature, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <CheckCircle className="w-3 h-3 text-green-400" />
                        <span className="text-gray-300">{feature}</span>
                      </div>
                    ))}
                    {capability.features.length > 3 && (
                      <p className="text-xs text-gray-500">
                        +{capability.features.length - 3} more features
                      </p>
                    )}
                  </div>
                  
                  {capability.usage_count && (
                    <div className="mt-4 pt-4 border-t border-gray-700">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-400">Usage Count</span>
                        <span className="text-blue-400">{capability.usage_count}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Other tab content would be implemented here */}
        {activeTab !== 'overview' && (
          <div className="bg-gray-800 rounded-lg p-8 text-center">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Settings className="w-8 h-8 text-blue-400" />
              <h3 className="text-xl font-semibold">
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Management
              </h3>
            </div>
            <p className="text-gray-400 mb-6">
              Advanced {activeTab} management features coming soon...
            </p>
            <button className="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg">
              Configure {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
            </button>
          </div>
        )}
      </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedSuperAdminDashboard;
