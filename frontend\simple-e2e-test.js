import { chromium } from 'playwright';

async function runBasicE2ETest() {
  console.log('🚀 Starting basic E2E test...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    // Test 1: Homepage loads
    console.log('📄 Testing homepage...');
    await page.goto('http://localhost:3000');
    await page.waitForSelector('h1', { timeout: 10000 });
    const title = await page.title();
    console.log(`✅ Homepage loaded: ${title}`);
    
    // Test 2: Navigation works
    console.log('🧭 Testing navigation...');
    await page.click('a[href="/login"]');
    await page.waitForURL('**/login');
    console.log('✅ Navigation to login page works');
    
    // Test 3: Login form exists
    console.log('📝 Testing login form...');
    const usernameInput = await page.locator('input[type="text"]').first();
    const passwordInput = await page.locator('input[type="password"]').first();
    const loginButton = await page.locator('button').filter({ hasText: 'تسجيل الدخول' });
    
    if (await usernameInput.isVisible() && await passwordInput.isVisible() && await loginButton.isVisible()) {
      console.log('✅ Login form elements are present');
    } else {
      console.log('❌ Login form elements missing');
    }
    
    // Test 4: Try login (will fail due to backend)
    console.log('🔐 Testing login attempt...');
    await usernameInput.fill('test_user');
    await passwordInput.fill('test_password');
    await loginButton.click();
    
    // Wait for error message
    await page.waitForTimeout(2000);
    const errorMessage = await page.locator('text=Failed to fetch').isVisible();
    if (errorMessage) {
      console.log('✅ Expected error message displayed (backend unavailable)');
    }
    
    // Test 5: Test responsive design
    console.log('📱 Testing responsive design...');
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('http://localhost:3000');
    const hamburgerMenu = await page.locator('button').filter({ hasText: 'Toggle menu' }).isVisible();
    if (hamburgerMenu) {
      console.log('✅ Mobile responsive design works');
    }
    
    // Test 6: Test other pages
    console.log('📄 Testing other pages...');
    const pages = ['/incubator', '/forum', '/templates'];
    
    for (const pagePath of pages) {
      try {
        await page.goto(`http://localhost:3000${pagePath}`);
        await page.waitForTimeout(2000);
        const pageTitle = await page.title();
        console.log(`✅ Page ${pagePath} loads: ${pageTitle}`);
      } catch (error) {
        console.log(`❌ Page ${pagePath} failed to load: ${error.message}`);
      }
    }
    
    console.log('🎉 Basic E2E test completed!');
    
  } catch (error) {
    console.error('❌ E2E test failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the test
runBasicE2ETest().catch(console.error);
