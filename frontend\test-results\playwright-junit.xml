<testsuites id="" name="" tests="126" failures="37" skipped="83" errors="0" time="131.06974400000001">
<testsuite name="admin-crud-workflow.test.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="chromium" tests="7" failures="3" skipped="4" time="73.646" errors="0">
<testcase name="Admin CRUD Workflows › Business Plans Management › should complete full CRUD workflow for business plans" classname="admin-crud-workflow.test.ts" time="24.474">
<failure message="admin-crud-workflow.test.ts:48:5 should complete full CRUD workflow for business plans" type="FAILURE">
<![CDATA[  [chromium] › admin-crud-workflow.test.ts:48:5 › Admin CRUD Workflows › Business Plans Management › should complete full CRUD workflow for business plans 

    TimeoutError: page.fill: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="email-input"]')


      14 | async function loginAsAdmin(page: Page) {
      15 |   await page.goto(`${BASE_URL}/login`);
    > 16 |   await page.fill('[data-testid="email-input"]', ADMIN_EMAIL);
         |              ^
      17 |   await page.fill('[data-testid="password-input"]', ADMIN_PASSWORD);
      18 |   await page.click('[data-testid="login-button"]');
      19 |   await page.waitForURL('**/admin/**');
        at loginAsAdmin (C:\Users\<USER>\Downloads\project-bolt-sb1-wl1xvo5f - Copy\frontend\src\__tests__\e2e\admin-crud-workflow.test.ts:16:14)
        at C:\Users\<USER>\Downloads\project-bolt-sb1-wl1xvo5f - Copy\frontend\src\__tests__\e2e\admin-crud-workflow.test.ts:44:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\..\test-results\playwright\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\..\test-results\playwright\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\playwright\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-chromium\test-failed-1.png]]

[[ATTACHMENT|playwright\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-chromium\video.webm]]

[[ATTACHMENT|playwright\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin CRUD Workflows › Business Plans Management › should handle search and filtering" classname="admin-crud-workflow.test.ts" time="23.721">
<failure message="admin-crud-workflow.test.ts:115:5 should handle search and filtering" type="FAILURE">
<![CDATA[  [chromium] › admin-crud-workflow.test.ts:115:5 › Admin CRUD Workflows › Business Plans Management › should handle search and filtering 

    TimeoutError: page.fill: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="email-input"]')


      14 | async function loginAsAdmin(page: Page) {
      15 |   await page.goto(`${BASE_URL}/login`);
    > 16 |   await page.fill('[data-testid="email-input"]', ADMIN_EMAIL);
         |              ^
      17 |   await page.fill('[data-testid="password-input"]', ADMIN_PASSWORD);
      18 |   await page.click('[data-testid="login-button"]');
      19 |   await page.waitForURL('**/admin/**');
        at loginAsAdmin (C:\Users\<USER>\Downloads\project-bolt-sb1-wl1xvo5f - Copy\frontend\src\__tests__\e2e\admin-crud-workflow.test.ts:16:14)
        at C:\Users\<USER>\Downloads\project-bolt-sb1-wl1xvo5f - Copy\frontend\src\__tests__\e2e\admin-crud-workflow.test.ts:44:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\..\test-results\playwright\admin-crud-workflow-Admin--d85ae-handle-search-and-filtering-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\..\test-results\playwright\admin-crud-workflow-Admin--d85ae-handle-search-and-filtering-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\playwright\admin-crud-workflow-Admin--d85ae-handle-search-and-filtering-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright\admin-crud-workflow-Admin--d85ae-handle-search-and-filtering-chromium\test-failed-1.png]]

[[ATTACHMENT|playwright\admin-crud-workflow-Admin--d85ae-handle-search-and-filtering-chromium\video.webm]]

[[ATTACHMENT|playwright\admin-crud-workflow-Admin--d85ae-handle-search-and-filtering-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin CRUD Workflows › Mentor Profiles Management › should complete full CRUD workflow for mentor profiles" classname="admin-crud-workflow.test.ts" time="25.451">
<failure message="admin-crud-workflow.test.ts:135:5 should complete full CRUD workflow for mentor profiles" type="FAILURE">
<![CDATA[  [chromium] › admin-crud-workflow.test.ts:135:5 › Admin CRUD Workflows › Mentor Profiles Management › should complete full CRUD workflow for mentor profiles 

    TimeoutError: page.fill: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="email-input"]')


      14 | async function loginAsAdmin(page: Page) {
      15 |   await page.goto(`${BASE_URL}/login`);
    > 16 |   await page.fill('[data-testid="email-input"]', ADMIN_EMAIL);
         |              ^
      17 |   await page.fill('[data-testid="password-input"]', ADMIN_PASSWORD);
      18 |   await page.click('[data-testid="login-button"]');
      19 |   await page.waitForURL('**/admin/**');
        at loginAsAdmin (C:\Users\<USER>\Downloads\project-bolt-sb1-wl1xvo5f - Copy\frontend\src\__tests__\e2e\admin-crud-workflow.test.ts:16:14)
        at C:\Users\<USER>\Downloads\project-bolt-sb1-wl1xvo5f - Copy\frontend\src\__tests__\e2e\admin-crud-workflow.test.ts:44:5

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\..\test-results\playwright\admin-crud-workflow-Admin--5d620-orkflow-for-mentor-profiles-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright\admin-crud-workflow-Admin--5d620-orkflow-for-mentor-profiles-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Admin CRUD Workflows › Funding Management › should complete full CRUD workflow for funding opportunities" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should complete full CRUD workflow for milestones" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should display analytics correctly" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Cross-Entity Integration › should handle relationships between entities" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="redux-diagnostics.spec.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="chromium" tests="11" failures="1" skipped="4" time="119.483" errors="0">
<testcase name="Redux Diagnostics Suite › 🔍 Redux Store Structure Validation" classname="redux-diagnostics.spec.ts" time="24.397">
<system-out>
<![CDATA[🧪 Testing Redux store structure...
✅ Present slices: [
  [32m'auth'[39m,      [32m'events'[39m,
  [32m'admin'[39m,     [32m'language'[39m,
  [32m'incubator'[39m, [32m'forum'[39m,
  [32m'ai'[39m,        [32m'businessPlans'[39m,
  [32m'aiContext'[39m, [32m'dashboard'[39m,
  [32m'toast'[39m,     [32m'ui'[39m
]
]]>
</system-out>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔐 Authentication State Consistency" classname="redux-diagnostics.spec.ts" time="12.463">
<system-out>
<![CDATA[🧪 Testing authentication state consistency...
🔍 Auth State Analysis: { hasToken: [33mfalse[39m, isAuthenticated: [33mfalse[39m, hasUser: [33mfalse[39m, user: [1mnull[22m }
]]>
</system-out>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Middleware Functionality Test" classname="redux-diagnostics.spec.ts" time="12.404">
<system-out>
<![CDATA[🧪 Testing Redux middleware...
]]>
</system-out>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚫 Context API Remnants Detection" classname="redux-diagnostics.spec.ts" time="15.337">
<system-out>
<![CDATA[🧪 Checking for Context API remnants...
🔍 Context providers found: []
]]>
</system-out>
</testcase>
<testcase name="Redux Diagnostics Suite › 📊 State Shape Validation" classname="redux-diagnostics.spec.ts" time="11.578">
<system-out>
<![CDATA[🧪 Validating Redux state shapes...
✅ All state shapes are valid
]]>
</system-out>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Action Dispatch and State Updates" classname="redux-diagnostics.spec.ts" time="11.452">
<system-out>
<![CDATA[🧪 Testing action dispatch and state updates...
✅ Action dispatch working correctly
]]>
</system-out>
</testcase>
<testcase name="Redux Diagnostics Suite › 🌐 Language State Integration" classname="redux-diagnostics.spec.ts" time="31.852">
<failure message="redux-diagnostics.spec.ts:254:3 🌐 Language State Integration" type="FAILURE">
<![CDATA[  [chromium] › redux-diagnostics.spec.ts:254:3 › Redux Diagnostics Suite › 🌐 Language State Integration 

    TimeoutError: page.waitForFunction: Timeout 10000ms exceeded.

      44 |     
      45 |     // Wait for Redux store to be available
    > 46 |     await page.waitForFunction(() => window.__REDUX_STORE__ || window.store);
         |                ^
      47 |   });
      48 |
      49 |   test('🔍 Redux Store Structure Validation', async () => {
        at C:\Users\<USER>\Downloads\project-bolt-sb1-wl1xvo5f - Copy\frontend\src\__tests__\e2e\redux-diagnostics.spec.ts:46:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\..\test-results\playwright\redux-diagnostics-Redux-Di-d7d6d--Language-State-Integration-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright\redux-diagnostics-Redux-Di-d7d6d--Language-State-Integration-chromium\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚨 Error Handling and Recovery" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔗 Token and Authentication Flow" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🎯 Redux DevTools Integration" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 📱 Responsive State Management" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-crud-workflow.test.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="firefox" tests="7" failures="7" skipped="0" time="0.2" errors="0">
<testcase name="Admin CRUD Workflows › Business Plans Management › should complete full CRUD workflow for business plans" classname="admin-crud-workflow.test.ts" time="0.014">
<failure message="admin-crud-workflow.test.ts:48:5 should complete full CRUD workflow for business plans" type="FAILURE">
<![CDATA[  [firefox] › admin-crud-workflow.test.ts:48:5 › Admin CRUD Workflows › Business Plans Management › should complete full CRUD workflow for business plans 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Admin CRUD Workflows › Business Plans Management › should handle search and filtering" classname="admin-crud-workflow.test.ts" time="0.038">
<failure message="admin-crud-workflow.test.ts:115:5 should handle search and filtering" type="FAILURE">
<![CDATA[  [firefox] › admin-crud-workflow.test.ts:115:5 › Admin CRUD Workflows › Business Plans Management › should handle search and filtering 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Admin CRUD Workflows › Mentor Profiles Management › should complete full CRUD workflow for mentor profiles" classname="admin-crud-workflow.test.ts" time="0.033">
<failure message="admin-crud-workflow.test.ts:135:5 should complete full CRUD workflow for mentor profiles" type="FAILURE">
<![CDATA[  [firefox] › admin-crud-workflow.test.ts:135:5 › Admin CRUD Workflows › Mentor Profiles Management › should complete full CRUD workflow for mentor profiles 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Admin CRUD Workflows › Funding Management › should complete full CRUD workflow for funding opportunities" classname="admin-crud-workflow.test.ts" time="0.032">
<failure message="admin-crud-workflow.test.ts:198:5 should complete full CRUD workflow for funding opportunities" type="FAILURE">
<![CDATA[  [firefox] › admin-crud-workflow.test.ts:198:5 › Admin CRUD Workflows › Funding Management › should complete full CRUD workflow for funding opportunities 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should complete full CRUD workflow for milestones" classname="admin-crud-workflow.test.ts" time="0.026">
<failure message="admin-crud-workflow.test.ts:267:5 should complete full CRUD workflow for milestones" type="FAILURE">
<![CDATA[  [firefox] › admin-crud-workflow.test.ts:267:5 › Admin CRUD Workflows › Milestones Management › should complete full CRUD workflow for milestones 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should display analytics correctly" classname="admin-crud-workflow.test.ts" time="0.026">
<failure message="admin-crud-workflow.test.ts:327:5 should display analytics correctly" type="FAILURE">
<![CDATA[  [firefox] › admin-crud-workflow.test.ts:327:5 › Admin CRUD Workflows › Milestones Management › should display analytics correctly 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Admin CRUD Workflows › Cross-Entity Integration › should handle relationships between entities" classname="admin-crud-workflow.test.ts" time="0.031">
<failure message="admin-crud-workflow.test.ts:343:5 should handle relationships between entities" type="FAILURE">
<![CDATA[  [firefox] › admin-crud-workflow.test.ts:343:5 › Admin CRUD Workflows › Cross-Entity Integration › should handle relationships between entities 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="redux-diagnostics.spec.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="firefox" tests="11" failures="11" skipped="0" time="0.501" errors="0">
<testcase name="Redux Diagnostics Suite › 🔍 Redux Store Structure Validation" classname="redux-diagnostics.spec.ts" time="0.021">
<failure message="redux-diagnostics.spec.ts:49:3 🔍 Redux Store Structure Validation" type="FAILURE">
<![CDATA[  [firefox] › redux-diagnostics.spec.ts:49:3 › Redux Diagnostics Suite › 🔍 Redux Store Structure Validation 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔐 Authentication State Consistency" classname="redux-diagnostics.spec.ts" time="0.011">
<failure message="redux-diagnostics.spec.ts:94:3 🔐 Authentication State Consistency" type="FAILURE">
<![CDATA[  [firefox] › redux-diagnostics.spec.ts:94:3 › Redux Diagnostics Suite › 🔐 Authentication State Consistency 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Middleware Functionality Test" classname="redux-diagnostics.spec.ts" time="0.102">
<failure message="redux-diagnostics.spec.ts:130:3 🔄 Middleware Functionality Test" type="FAILURE">
<![CDATA[  [firefox] › redux-diagnostics.spec.ts:130:3 › Redux Diagnostics Suite › 🔄 Middleware Functionality Test 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚫 Context API Remnants Detection" classname="redux-diagnostics.spec.ts" time="0.05">
<failure message="redux-diagnostics.spec.ts:163:3 🚫 Context API Remnants Detection" type="FAILURE">
<![CDATA[  [firefox] › redux-diagnostics.spec.ts:163:3 › Redux Diagnostics Suite › 🚫 Context API Remnants Detection 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 📊 State Shape Validation" classname="redux-diagnostics.spec.ts" time="0.025">
<failure message="redux-diagnostics.spec.ts:194:3 📊 State Shape Validation" type="FAILURE">
<![CDATA[  [firefox] › redux-diagnostics.spec.ts:194:3 › Redux Diagnostics Suite › 📊 State Shape Validation 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Action Dispatch and State Updates" classname="redux-diagnostics.spec.ts" time="0.031">
<failure message="redux-diagnostics.spec.ts:226:3 🔄 Action Dispatch and State Updates" type="FAILURE">
<![CDATA[  [firefox] › redux-diagnostics.spec.ts:226:3 › Redux Diagnostics Suite › 🔄 Action Dispatch and State Updates 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🌐 Language State Integration" classname="redux-diagnostics.spec.ts" time="0.026">
<failure message="redux-diagnostics.spec.ts:254:3 🌐 Language State Integration" type="FAILURE">
<![CDATA[  [firefox] › redux-diagnostics.spec.ts:254:3 › Redux Diagnostics Suite › 🌐 Language State Integration 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚨 Error Handling and Recovery" classname="redux-diagnostics.spec.ts" time="0.057">
<failure message="redux-diagnostics.spec.ts:285:3 🚨 Error Handling and Recovery" type="FAILURE">
<![CDATA[  [firefox] › redux-diagnostics.spec.ts:285:3 › Redux Diagnostics Suite › 🚨 Error Handling and Recovery 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔗 Token and Authentication Flow" classname="redux-diagnostics.spec.ts" time="0.03">
<failure message="redux-diagnostics.spec.ts:320:3 🔗 Token and Authentication Flow" type="FAILURE">
<![CDATA[  [firefox] › redux-diagnostics.spec.ts:320:3 › Redux Diagnostics Suite › 🔗 Token and Authentication Flow 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🎯 Redux DevTools Integration" classname="redux-diagnostics.spec.ts" time="0.128">
<failure message="redux-diagnostics.spec.ts:345:3 🎯 Redux DevTools Integration" type="FAILURE">
<![CDATA[  [firefox] › redux-diagnostics.spec.ts:345:3 › Redux Diagnostics Suite › 🎯 Redux DevTools Integration 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 📱 Responsive State Management" classname="redux-diagnostics.spec.ts" time="0.02">
<failure message="redux-diagnostics.spec.ts:361:3 📱 Responsive State Management" type="FAILURE">
<![CDATA[  [firefox] › redux-diagnostics.spec.ts:361:3 › Redux Diagnostics Suite › 📱 Responsive State Management 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="admin-crud-workflow.test.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="webkit" tests="7" failures="7" skipped="0" time="0.233" errors="0">
<testcase name="Admin CRUD Workflows › Business Plans Management › should complete full CRUD workflow for business plans" classname="admin-crud-workflow.test.ts" time="0.022">
<failure message="admin-crud-workflow.test.ts:48:5 should complete full CRUD workflow for business plans" type="FAILURE">
<![CDATA[  [webkit] › admin-crud-workflow.test.ts:48:5 › Admin CRUD Workflows › Business Plans Management › should complete full CRUD workflow for business plans 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Admin CRUD Workflows › Business Plans Management › should handle search and filtering" classname="admin-crud-workflow.test.ts" time="0.024">
<failure message="admin-crud-workflow.test.ts:115:5 should handle search and filtering" type="FAILURE">
<![CDATA[  [webkit] › admin-crud-workflow.test.ts:115:5 › Admin CRUD Workflows › Business Plans Management › should handle search and filtering 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Admin CRUD Workflows › Mentor Profiles Management › should complete full CRUD workflow for mentor profiles" classname="admin-crud-workflow.test.ts" time="0.061">
<failure message="admin-crud-workflow.test.ts:135:5 should complete full CRUD workflow for mentor profiles" type="FAILURE">
<![CDATA[  [webkit] › admin-crud-workflow.test.ts:135:5 › Admin CRUD Workflows › Mentor Profiles Management › should complete full CRUD workflow for mentor profiles 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Admin CRUD Workflows › Funding Management › should complete full CRUD workflow for funding opportunities" classname="admin-crud-workflow.test.ts" time="0.021">
<failure message="admin-crud-workflow.test.ts:198:5 should complete full CRUD workflow for funding opportunities" type="FAILURE">
<![CDATA[  [webkit] › admin-crud-workflow.test.ts:198:5 › Admin CRUD Workflows › Funding Management › should complete full CRUD workflow for funding opportunities 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should complete full CRUD workflow for milestones" classname="admin-crud-workflow.test.ts" time="0.018">
<failure message="admin-crud-workflow.test.ts:267:5 should complete full CRUD workflow for milestones" type="FAILURE">
<![CDATA[  [webkit] › admin-crud-workflow.test.ts:267:5 › Admin CRUD Workflows › Milestones Management › should complete full CRUD workflow for milestones 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should display analytics correctly" classname="admin-crud-workflow.test.ts" time="0.068">
<failure message="admin-crud-workflow.test.ts:327:5 should display analytics correctly" type="FAILURE">
<![CDATA[  [webkit] › admin-crud-workflow.test.ts:327:5 › Admin CRUD Workflows › Milestones Management › should display analytics correctly 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Admin CRUD Workflows › Cross-Entity Integration › should handle relationships between entities" classname="admin-crud-workflow.test.ts" time="0.019">
<failure message="admin-crud-workflow.test.ts:343:5 should handle relationships between entities" type="FAILURE">
<![CDATA[  [webkit] › admin-crud-workflow.test.ts:343:5 › Admin CRUD Workflows › Cross-Entity Integration › should handle relationships between entities 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="redux-diagnostics.spec.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="webkit" tests="11" failures="7" skipped="4" time="0.288" errors="0">
<testcase name="Redux Diagnostics Suite › 🔍 Redux Store Structure Validation" classname="redux-diagnostics.spec.ts" time="0.019">
<failure message="redux-diagnostics.spec.ts:49:3 🔍 Redux Store Structure Validation" type="FAILURE">
<![CDATA[  [webkit] › redux-diagnostics.spec.ts:49:3 › Redux Diagnostics Suite › 🔍 Redux Store Structure Validation 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔐 Authentication State Consistency" classname="redux-diagnostics.spec.ts" time="0.066">
<failure message="redux-diagnostics.spec.ts:94:3 🔐 Authentication State Consistency" type="FAILURE">
<![CDATA[  [webkit] › redux-diagnostics.spec.ts:94:3 › Redux Diagnostics Suite › 🔐 Authentication State Consistency 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Middleware Functionality Test" classname="redux-diagnostics.spec.ts" time="0.019">
<failure message="redux-diagnostics.spec.ts:130:3 🔄 Middleware Functionality Test" type="FAILURE">
<![CDATA[  [webkit] › redux-diagnostics.spec.ts:130:3 › Redux Diagnostics Suite › 🔄 Middleware Functionality Test 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚫 Context API Remnants Detection" classname="redux-diagnostics.spec.ts" time="0.068">
<failure message="redux-diagnostics.spec.ts:163:3 🚫 Context API Remnants Detection" type="FAILURE">
<![CDATA[  [webkit] › redux-diagnostics.spec.ts:163:3 › Redux Diagnostics Suite › 🚫 Context API Remnants Detection 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 📊 State Shape Validation" classname="redux-diagnostics.spec.ts" time="0.042">
<failure message="redux-diagnostics.spec.ts:194:3 📊 State Shape Validation" type="FAILURE">
<![CDATA[  [webkit] › redux-diagnostics.spec.ts:194:3 › Redux Diagnostics Suite › 📊 State Shape Validation ─

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Action Dispatch and State Updates" classname="redux-diagnostics.spec.ts" time="0.034">
<failure message="redux-diagnostics.spec.ts:226:3 🔄 Action Dispatch and State Updates" type="FAILURE">
<![CDATA[  [webkit] › redux-diagnostics.spec.ts:226:3 › Redux Diagnostics Suite › 🔄 Action Dispatch and State Updates 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🌐 Language State Integration" classname="redux-diagnostics.spec.ts" time="0.037">
<failure message="redux-diagnostics.spec.ts:254:3 🌐 Language State Integration" type="FAILURE">
<![CDATA[  [webkit] › redux-diagnostics.spec.ts:254:3 › Redux Diagnostics Suite › 🌐 Language State Integration 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚨 Error Handling and Recovery" classname="redux-diagnostics.spec.ts" time="0.003">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔗 Token and Authentication Flow" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🎯 Redux DevTools Integration" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 📱 Responsive State Management" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-crud-workflow.test.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="Mobile Chrome" tests="7" failures="1" skipped="6" time="29.151" errors="0">
<testcase name="Admin CRUD Workflows › Business Plans Management › should complete full CRUD workflow for business plans" classname="admin-crud-workflow.test.ts" time="29.151">
<failure message="admin-crud-workflow.test.ts:48:5 should complete full CRUD workflow for business plans" type="FAILURE">
<![CDATA[  [Mobile Chrome] › admin-crud-workflow.test.ts:48:5 › Admin CRUD Workflows › Business Plans Management › should complete full CRUD workflow for business plans 

    TimeoutError: page.fill: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="email-input"]')


      14 | async function loginAsAdmin(page: Page) {
      15 |   await page.goto(`${BASE_URL}/login`);
    > 16 |   await page.fill('[data-testid="email-input"]', ADMIN_EMAIL);
         |              ^
      17 |   await page.fill('[data-testid="password-input"]', ADMIN_PASSWORD);
      18 |   await page.click('[data-testid="login-button"]');
      19 |   await page.waitForURL('**/admin/**');
        at loginAsAdmin (C:\Users\<USER>\Downloads\project-bolt-sb1-wl1xvo5f - Copy\frontend\src\__tests__\e2e\admin-crud-workflow.test.ts:16:14)
        at C:\Users\<USER>\Downloads\project-bolt-sb1-wl1xvo5f - Copy\frontend\src\__tests__\e2e\admin-crud-workflow.test.ts:44:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\..\test-results\playwright\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\..\test-results\playwright\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|playwright\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|playwright\admin-crud-workflow-Admin--8181e-workflow-for-business-plans-Mobile-Chrome\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Admin CRUD Workflows › Business Plans Management › should handle search and filtering" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Mentor Profiles Management › should complete full CRUD workflow for mentor profiles" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Funding Management › should complete full CRUD workflow for funding opportunities" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should complete full CRUD workflow for milestones" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should display analytics correctly" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Cross-Entity Integration › should handle relationships between entities" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="redux-diagnostics.spec.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="Mobile Chrome" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Redux Diagnostics Suite › 🔍 Redux Store Structure Validation" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔐 Authentication State Consistency" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Middleware Functionality Test" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚫 Context API Remnants Detection" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 📊 State Shape Validation" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Action Dispatch and State Updates" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🌐 Language State Integration" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚨 Error Handling and Recovery" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔗 Token and Authentication Flow" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🎯 Redux DevTools Integration" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 📱 Responsive State Management" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-crud-workflow.test.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="Mobile Safari" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Admin CRUD Workflows › Business Plans Management › should complete full CRUD workflow for business plans" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Business Plans Management › should handle search and filtering" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Mentor Profiles Management › should complete full CRUD workflow for mentor profiles" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Funding Management › should complete full CRUD workflow for funding opportunities" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should complete full CRUD workflow for milestones" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should display analytics correctly" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Cross-Entity Integration › should handle relationships between entities" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="redux-diagnostics.spec.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="Mobile Safari" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Redux Diagnostics Suite › 🔍 Redux Store Structure Validation" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔐 Authentication State Consistency" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Middleware Functionality Test" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚫 Context API Remnants Detection" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 📊 State Shape Validation" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Action Dispatch and State Updates" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🌐 Language State Integration" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚨 Error Handling and Recovery" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔗 Token and Authentication Flow" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🎯 Redux DevTools Integration" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 📱 Responsive State Management" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-crud-workflow.test.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="Microsoft Edge" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Admin CRUD Workflows › Business Plans Management › should complete full CRUD workflow for business plans" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Business Plans Management › should handle search and filtering" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Mentor Profiles Management › should complete full CRUD workflow for mentor profiles" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Funding Management › should complete full CRUD workflow for funding opportunities" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should complete full CRUD workflow for milestones" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should display analytics correctly" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Cross-Entity Integration › should handle relationships between entities" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="redux-diagnostics.spec.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="Microsoft Edge" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Redux Diagnostics Suite › 🔍 Redux Store Structure Validation" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔐 Authentication State Consistency" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Middleware Functionality Test" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚫 Context API Remnants Detection" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 📊 State Shape Validation" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Action Dispatch and State Updates" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🌐 Language State Integration" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚨 Error Handling and Recovery" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔗 Token and Authentication Flow" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🎯 Redux DevTools Integration" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 📱 Responsive State Management" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-crud-workflow.test.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="Google Chrome" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Admin CRUD Workflows › Business Plans Management › should complete full CRUD workflow for business plans" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Business Plans Management › should handle search and filtering" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Mentor Profiles Management › should complete full CRUD workflow for mentor profiles" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Funding Management › should complete full CRUD workflow for funding opportunities" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should complete full CRUD workflow for milestones" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Milestones Management › should display analytics correctly" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin CRUD Workflows › Cross-Entity Integration › should handle relationships between entities" classname="admin-crud-workflow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="redux-diagnostics.spec.ts" timestamp="2025-07-16T17:33:45.354Z" hostname="Google Chrome" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Redux Diagnostics Suite › 🔍 Redux Store Structure Validation" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔐 Authentication State Consistency" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Middleware Functionality Test" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚫 Context API Remnants Detection" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 📊 State Shape Validation" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔄 Action Dispatch and State Updates" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🌐 Language State Integration" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🚨 Error Handling and Recovery" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🔗 Token and Authentication Flow" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 🎯 Redux DevTools Integration" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Redux Diagnostics Suite › 📱 Responsive State Management" classname="redux-diagnostics.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>