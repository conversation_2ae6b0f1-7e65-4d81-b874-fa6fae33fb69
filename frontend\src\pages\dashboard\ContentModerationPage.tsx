import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { AlertTriangle, CheckCircle, X, Eye, Flag, Search, Filter, Clock, User } from 'lucide-react';

interface ContentItem {
  id: string;
  type: 'post' | 'comment' | 'business_idea' | 'resource';
  title: string;
  content: string;
  author: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  reportedAt?: string;
  reportedBy?: string;
  reportReason?: string;
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  flags: number;
  views: number;
}

const ContentModerationPage: React.FC = () => {
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<ContentItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('pending');

  // Load real content moderation data from API
  useEffect(() => {
    // TODO: Replace with actual API call to fetch content moderation items
    // const fetchContentItems = async () => {
    //   try {
    //     const response = await api.get('/admin/content-moderation/');
    //     setItems(response.data.results);
    //   } catch (error) {
    //     console.error('Failed to fetch content items:', error);
    //   }
    // };
    // fetchContentItems();
      {
        id: '2',
        type: 'comment',
        title: 'Comment on "AI in Healthcare"',
        content: 'This is a great article about AI applications in healthcare. However, I think there are some concerns...',
        author: {
          id: 'user3',
          name: 'Sarah Johnson',
          email: '<EMAIL>'
        },
        createdAt: '2024-01-14T16:45:00Z',
        status: 'approved',
        priority: 'low',
        category: 'Technology',
        flags: 0,
        views: 89
      },
      {
        id: '3',
        type: 'business_idea',
        title: 'Sustainable Food Delivery Platform',
        content: 'A platform that connects local farmers directly with consumers, reducing food waste and carbon footprint...',
        author: {
          id: 'user4',
          name: 'Michael Chen',
          email: '<EMAIL>'
        },
        createdAt: '2024-01-13T09:15:00Z',
        reportedAt: '2024-01-14T11:30:00Z',
        reportedBy: 'user5',
        reportReason: 'Inappropriate content',
        status: 'flagged',
        priority: 'high',
        category: 'Sustainability',
        flags: 5,
        views: 234
      },
      {
        id: '4',
        type: 'resource',
        title: 'Complete Guide to Startup Funding',
        content: 'This comprehensive guide covers all aspects of startup funding from seed to Series A...',
        author: {
          id: 'user6',
          name: 'Emily Davis',
          email: '<EMAIL>'
        },
        createdAt: '2024-01-12T14:20:00Z',
        status: 'rejected',
        priority: 'low',
        category: 'Education',
        flags: 1,
        views: 67
      }
    ];

    setTimeout(() => {
      setContentItems(mockItems);
      setFilteredItems(mockItems);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter content based on search and filters
  useEffect(() => {
    let filtered = contentItems;

    if (searchTerm) {
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.author.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(item => item.status === statusFilter);
    }

    if (typeFilter !== 'all') {
      filtered = filtered.filter(item => item.type === typeFilter);
    }

    if (priorityFilter !== 'all') {
      filtered = filtered.filter(item => item.priority === priorityFilter);
    }

    // Tab-specific filtering
    if (activeTab === 'pending') {
      filtered = filtered.filter(item => item.status === 'pending');
    } else if (activeTab === 'flagged') {
      filtered = filtered.filter(item => item.status === 'flagged');
    } else if (activeTab === 'approved') {
      filtered = filtered.filter(item => item.status === 'approved');
    } else if (activeTab === 'rejected') {
      filtered = filtered.filter(item => item.status === 'rejected');
    }

    setFilteredItems(filtered);
  }, [contentItems, searchTerm, statusFilter, typeFilter, priorityFilter, activeTab]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'flagged': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'post': return '📝';
      case 'comment': return '💬';
      case 'business_idea': return '💡';
      case 'resource': return '📚';
      default: return '📄';
    }
  };

  const handleApprove = (id: string) => {
    setContentItems(items => 
      items.map(item => 
        item.id === id ? { ...item, status: 'approved' as const } : item
      )
    );
  };

  const handleReject = (id: string) => {
    setContentItems(items => 
      items.map(item => 
        item.id === id ? { ...item, status: 'rejected' as const } : item
      )
    );
  };

  const handleFlag = (id: string) => {
    setContentItems(items => 
      items.map(item => 
        item.id === id ? { ...item, status: 'flagged' as const, flags: item.flags + 1 } : item
      )
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-48 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Content Moderation</h1>
          <p className="text-gray-600 mt-1">Review and moderate user-generated content</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Advanced Filters
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Eye className="w-4 h-4 mr-2" />
            Bulk Actions
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Review</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {contentItems.filter(item => item.status === 'pending').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Flagged Content</p>
                <p className="text-2xl font-bold text-orange-600">
                  {contentItems.filter(item => item.status === 'flagged').length}
                </p>
              </div>
              <Flag className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Approved Today</p>
                <p className="text-2xl font-bold text-green-600">
                  {contentItems.filter(item => item.status === 'approved').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Items</p>
                <p className="text-2xl font-bold">{contentItems.length}</p>
              </div>
              <User className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search content..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select 
              value={typeFilter} 
              onChange={(e) => setTypeFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Types</option>
              <option value="post">Posts</option>
              <option value="comment">Comments</option>
              <option value="business_idea">Business Ideas</option>
              <option value="resource">Resources</option>
            </select>
            <select 
              value={priorityFilter} 
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Priorities</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
            <select 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="flagged">Flagged</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="pending">Pending ({contentItems.filter(item => item.status === 'pending').length})</TabsTrigger>
          <TabsTrigger value="flagged">Flagged ({contentItems.filter(item => item.status === 'flagged').length})</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
          <TabsTrigger value="all">All Content</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          <div className="space-y-4">
            {filteredItems.map((item) => (
              <Card key={item.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start gap-3 flex-1">
                      <div className="text-2xl">{getTypeIcon(item.type)}</div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg">{item.title}</h3>
                          <Badge className={getStatusColor(item.status)}>
                            {item.status}
                          </Badge>
                          <span className={`text-sm font-medium ${getPriorityColor(item.priority)}`}>
                            {item.priority} priority
                          </span>
                        </div>
                        <p className="text-gray-700 mb-3 line-clamp-2">{item.content}</p>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                          <span>By: {item.author.name}</span>
                          <span>Category: {item.category}</span>
                          <span>Created: {formatDate(item.createdAt)}</span>
                          <span>Views: {item.views}</span>
                          {item.flags > 0 && (
                            <span className="text-red-600">Flags: {item.flags}</span>
                          )}
                        </div>

                        {item.reportedAt && (
                          <div className="bg-red-50 p-3 rounded mb-3">
                            <p className="text-sm text-red-800">
                              <strong>Reported:</strong> {formatDate(item.reportedAt)}
                            </p>
                            {item.reportReason && (
                              <p className="text-sm text-red-700">
                                <strong>Reason:</strong> {item.reportReason}
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 ml-4">
                      {item.status === 'pending' && (
                        <>
                          <Button 
                            size="sm" 
                            className="bg-green-600 hover:bg-green-700"
                            onClick={() => handleApprove(item.id)}
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Approve
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            className="border-red-300 text-red-600 hover:bg-red-50"
                            onClick={() => handleReject(item.id)}
                          >
                            <X className="w-4 h-4 mr-1" />
                            Reject
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            className="border-orange-300 text-orange-600 hover:bg-orange-50"
                            onClick={() => handleFlag(item.id)}
                          >
                            <Flag className="w-4 h-4 mr-1" />
                            Flag
                          </Button>
                        </>
                      )}
                      
                      {item.status === 'flagged' && (
                        <>
                          <Button 
                            size="sm" 
                            className="bg-green-600 hover:bg-green-700"
                            onClick={() => handleApprove(item.id)}
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Approve
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            className="border-red-300 text-red-600 hover:bg-red-50"
                            onClick={() => handleReject(item.id)}
                          >
                            <X className="w-4 h-4 mr-1" />
                            Remove
                          </Button>
                        </>
                      )}

                      <Button size="sm" variant="outline">
                        <Eye className="w-4 h-4 mr-1" />
                        View Full
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredItems.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center">
                <AlertTriangle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">
                  No content found matching your criteria.
                </p>
                <Button variant="outline" onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setTypeFilter('all');
                  setPriorityFilter('all');
                }}>
                  Clear Filters
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ContentModerationPage;
