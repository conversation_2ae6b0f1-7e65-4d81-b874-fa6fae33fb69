#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set the settings module explicitly
os.environ['DJANGO_SETTINGS_MODULE'] = 'yasmeen_ai.settings'

# Setup Django
django.setup()

# Print debug information
from django.conf import settings
print(f"DJANGO_SETTINGS_MODULE: {os.environ.get('DJANGO_SETTINGS_MODULE')}")
print(f"Settings module: {settings.SETTINGS_MODULE}")
print(f"ROOT_URLCONF: {settings.ROOT_URLCONF}")
print(f"DEBUG: {settings.DEBUG}")

# Try to import the URLs module
try:
    import yasmeen_ai.urls
    print("yasmeen_ai.urls imported successfully")
    print(f"Number of URL patterns: {len(yasmeen_ai.urls.urlpatterns)}")
    for i, pattern in enumerate(yasmeen_ai.urls.urlpatterns):
        print(f"  {i}: {pattern}")
except Exception as e:
    print(f"Error importing yasmeen_ai.urls: {e}")

# Check if urls_minimal exists and what it contains
try:
    import yasmeen_ai.urls_minimal
    print("yasmeen_ai.urls_minimal imported successfully")
    print(f"Number of minimal URL patterns: {len(yasmeen_ai.urls_minimal.urlpatterns)}")
    for i, pattern in enumerate(yasmeen_ai.urls_minimal.urlpatterns):
        print(f"  {i}: {pattern}")
except Exception as e:
    print(f"Error importing yasmeen_ai.urls_minimal: {e}")