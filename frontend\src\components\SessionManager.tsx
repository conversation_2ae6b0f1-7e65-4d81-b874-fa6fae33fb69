import React, { useEffect } from 'react';
import { useSessionManager } from '../hooks/useSessionManager';

/**
 * Session Manager Component
 * Handles session restoration and monitoring globally
 * This component should be mounted at the app root level
 */
const SessionManager: React.FC = () => {
  const { isAuthenticated, user } = useSessionManager();

  useEffect(() => {
    if (isAuthenticated && user) {
      console.log(`✅ Session active for user: ${user.username}`);
    }
  }, [isAuthenticated, user]);

  // This component doesn't render anything visible
  return null;
};

export default SessionManager;
