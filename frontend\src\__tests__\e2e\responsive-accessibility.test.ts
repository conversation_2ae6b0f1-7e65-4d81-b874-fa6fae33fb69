/**
 * Responsive Design and Accessibility Tests
 * Tests mobile responsiveness, accessibility compliance, and cross-browser compatibility
 */

import { test, expect, Page, devices } from '@playwright/test';

const BASE_URL = process.env.REACT_APP_BASE_URL || 'http://localhost:3000';

// Device configurations for responsive testing
const DEVICE_CONFIGS = [
  { name: 'Mobile', ...devices['iPhone 12'] },
  { name: 'Tablet', ...devices['iPad'] },
  { name: 'Desktop', viewport: { width: 1920, height: 1080 } }
];

// Accessibility test utilities
async function checkAccessibility(page: Page, context: string) {
  // Check for basic accessibility requirements
  const results = {
    context,
    issues: [] as string[],
    passed: true
  };

  // Check for alt text on images
  const images = await page.locator('img').all();
  for (const img of images) {
    const alt = await img.getAttribute('alt');
    if (!alt || alt.trim() === '') {
      results.issues.push('Image missing alt text');
      results.passed = false;
    }
  }

  // Check for form labels
  const inputs = await page.locator('input, textarea, select').all();
  for (const input of inputs) {
    const id = await input.getAttribute('id');
    const ariaLabel = await input.getAttribute('aria-label');
    const ariaLabelledBy = await input.getAttribute('aria-labelledby');
    
    if (id) {
      const label = await page.locator(`label[for="${id}"]`).count();
      if (label === 0 && !ariaLabel && !ariaLabelledBy) {
        results.issues.push(`Input field missing label: ${id}`);
        results.passed = false;
      }
    }
  }

  // Check for heading hierarchy
  const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
  let previousLevel = 0;
  for (const heading of headings) {
    const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
    const currentLevel = parseInt(tagName.charAt(1));
    
    if (currentLevel > previousLevel + 1) {
      results.issues.push(`Heading hierarchy skip: ${tagName} after h${previousLevel}`);
      results.passed = false;
    }
    previousLevel = currentLevel;
  }

  // Check for keyboard navigation
  const focusableElements = await page.locator('button, a, input, textarea, select, [tabindex]:not([tabindex="-1"])').all();
  if (focusableElements.length === 0) {
    results.issues.push('No focusable elements found');
    results.passed = false;
  }

  return results;
}

async function testKeyboardNavigation(page: Page) {
  // Test tab navigation
  await page.keyboard.press('Tab');
  const firstFocused = await page.evaluate(() => document.activeElement?.tagName);
  expect(firstFocused).toBeTruthy();

  // Test escape key functionality
  const modal = page.locator('[role="dialog"], .modal');
  if (await modal.isVisible()) {
    await page.keyboard.press('Escape');
    await expect(modal).not.toBeVisible();
  }
}

test.describe('Responsive Design Tests', () => {
  DEVICE_CONFIGS.forEach(device => {
    test.describe(`${device.name} Device`, () => {
      test.use(device);

      test('should display navigation correctly', async ({ page }) => {
        await page.goto(`${BASE_URL}`);
        
        if (device.name === 'Mobile') {
          // Check for mobile menu toggle
          await expect(page.locator('[data-testid="mobile-menu-toggle"]')).toBeVisible();
          
          // Test mobile menu functionality
          await page.click('[data-testid="mobile-menu-toggle"]');
          await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
          
          // Test menu close
          await page.click('[data-testid="mobile-menu-close"]');
          await expect(page.locator('[data-testid="mobile-menu"]')).not.toBeVisible();
        } else {
          // Check for desktop navigation
          await expect(page.locator('[data-testid="desktop-navigation"]')).toBeVisible();
        }
      });

      test('should handle forms responsively', async ({ page }) => {
        await page.goto(`${BASE_URL}/register`);
        
        // Check form layout
        const form = page.locator('[data-testid="register-form"]');
        await expect(form).toBeVisible();
        
        // Test form inputs are accessible
        const inputs = await form.locator('input').all();
        for (const input of inputs) {
          await expect(input).toBeVisible();
          
          // Check input is not cut off
          const boundingBox = await input.boundingBox();
          expect(boundingBox?.width).toBeGreaterThan(0);
        }
        
        // Test form submission on different devices
        await page.fill('[data-testid="first-name-input"]', 'Test');
        await page.fill('[data-testid="last-name-input"]', 'User');
        await page.fill('[data-testid="email-input"]', '<EMAIL>');
        await page.fill('[data-testid="password-input"]', 'testpassword123');
        await page.fill('[data-testid="confirm-password-input"]', 'testpassword123');
        
        const submitButton = page.locator('[data-testid="register-button"]');
        await expect(submitButton).toBeVisible();
        await expect(submitButton).toBeEnabled();
      });

      test('should display dashboard cards properly', async ({ page }) => {
        // Mock login
        await page.goto(`${BASE_URL}/dashboard`);
        await page.evaluate(() => {
          localStorage.setItem('auth_token', 'mock_token');
        });
        await page.reload();
        
        const dashboardCards = page.locator('[data-testid="dashboard-card"]');
        const cardCount = await dashboardCards.count();
        
        if (cardCount > 0) {
          // Check cards are properly sized
          for (let i = 0; i < cardCount; i++) {
            const card = dashboardCards.nth(i);
            const boundingBox = await card.boundingBox();
            
            expect(boundingBox?.width).toBeGreaterThan(0);
            expect(boundingBox?.height).toBeGreaterThan(0);
            
            // Cards should not overflow viewport
            if (device.name === 'Mobile') {
              expect(boundingBox?.width).toBeLessThanOrEqual(device.viewport?.width || 375);
            }
          }
        }
      });

      test('should handle tables responsively', async ({ page }) => {
        await page.goto(`${BASE_URL}/dashboard/business-ideas`);
        
        const table = page.locator('[data-testid="business-ideas-table"]');
        if (await table.isVisible()) {
          if (device.name === 'Mobile') {
            // Mobile should show card view or horizontal scroll
            const cardView = page.locator('[data-testid="mobile-card-view"]');
            const scrollableTable = page.locator('[data-testid="scrollable-table"]');
            
            const hasCardView = await cardView.isVisible();
            const hasScrollableTable = await scrollableTable.isVisible();
            
            expect(hasCardView || hasScrollableTable).toBe(true);
          } else {
            // Desktop/tablet should show full table
            await expect(table).toBeVisible();
          }
        }
      });
    });
  });
});

test.describe('Accessibility Tests', () => {
  test('should meet WCAG accessibility standards on homepage', async ({ page }) => {
    await page.goto(`${BASE_URL}`);
    
    const results = await checkAccessibility(page, 'Homepage');
    
    if (!results.passed) {
      console.log('Accessibility issues found:', results.issues);
    }
    
    expect(results.passed).toBe(true);
  });

  test('should support keyboard navigation', async ({ page }) => {
    await page.goto(`${BASE_URL}`);
    
    await testKeyboardNavigation(page);
    
    // Test specific keyboard interactions
    const loginLink = page.locator('[data-testid="login-link"]');
    if (await loginLink.isVisible()) {
      await loginLink.focus();
      await page.keyboard.press('Enter');
      await page.waitForURL('**/login**');
    }
  });

  test('should have proper color contrast', async ({ page }) => {
    await page.goto(`${BASE_URL}`);
    
    // Check for sufficient color contrast (simplified check)
    const textElements = await page.locator('p, h1, h2, h3, h4, h5, h6, span, a, button').all();
    
    for (const element of textElements.slice(0, 10)) { // Check first 10 elements
      const styles = await element.evaluate(el => {
        const computed = window.getComputedStyle(el);
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor,
          fontSize: computed.fontSize
        };
      });
      
      // Basic check - ensure text is not transparent
      expect(styles.color).not.toBe('rgba(0, 0, 0, 0)');
      expect(styles.color).not.toBe('transparent');
    }
  });

  test('should support screen readers', async ({ page }) => {
    await page.goto(`${BASE_URL}`);
    
    // Check for ARIA landmarks
    const landmarks = await page.locator('[role="main"], [role="navigation"], [role="banner"], [role="contentinfo"]').count();
    expect(landmarks).toBeGreaterThan(0);
    
    // Check for skip links
    const skipLink = page.locator('[data-testid="skip-to-content"], a[href="#main-content"]');
    if (await skipLink.count() > 0) {
      await expect(skipLink.first()).toBeVisible();
    }
    
    // Check for proper heading structure
    const h1Count = await page.locator('h1').count();
    expect(h1Count).toBeGreaterThanOrEqual(1);
    expect(h1Count).toBeLessThanOrEqual(1); // Should have exactly one h1
  });

  test('should handle focus management in modals', async ({ page }) => {
    await page.goto(`${BASE_URL}/dashboard/business-ideas`);
    
    // Open a modal if available
    const createButton = page.locator('[data-testid="create-idea-button"]');
    if (await createButton.isVisible()) {
      await createButton.click();
      
      const modal = page.locator('[role="dialog"]');
      await expect(modal).toBeVisible();
      
      // Check focus is trapped in modal
      const focusedElement = await page.evaluate(() => document.activeElement?.tagName);
      expect(focusedElement).toBeTruthy();
      
      // Test escape key closes modal
      await page.keyboard.press('Escape');
      await expect(modal).not.toBeVisible();
    }
  });
});

test.describe('RTL (Arabic) Support Tests', () => {
  test('should display Arabic text correctly', async ({ page }) => {
    // Set Arabic language
    await page.goto(`${BASE_URL}`);
    await page.evaluate(() => {
      localStorage.setItem('i18nextLng', 'ar');
    });
    await page.reload();
    
    // Check for RTL direction
    const htmlDir = await page.locator('html').getAttribute('dir');
    expect(htmlDir).toBe('rtl');
    
    // Check for Arabic text rendering
    const arabicText = page.locator('text=/[\u0600-\u06FF]/');
    if (await arabicText.count() > 0) {
      await expect(arabicText.first()).toBeVisible();
    }
  });

  test('should handle RTL layout correctly', async ({ page }) => {
    await page.goto(`${BASE_URL}`);
    await page.evaluate(() => {
      localStorage.setItem('i18nextLng', 'ar');
    });
    await page.reload();
    
    // Check navigation alignment
    const navigation = page.locator('[data-testid="main-navigation"]');
    if (await navigation.isVisible()) {
      const styles = await navigation.evaluate(el => {
        const computed = window.getComputedStyle(el);
        return {
          direction: computed.direction,
          textAlign: computed.textAlign
        };
      });
      
      expect(styles.direction).toBe('rtl');
    }
  });
});

test.describe('Performance Tests', () => {
  test('should load pages within performance budgets', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto(`${BASE_URL}`);
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(5000); // 5 second budget
  });

  test('should handle large datasets without performance degradation', async ({ page }) => {
    await page.goto(`${BASE_URL}/dashboard/business-ideas`);
    
    // Mock large dataset
    await page.route('**/api/incubator/business-ideas/**', route => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        title: `Business Idea ${i + 1}`,
        description: `Description ${i + 1}`
      }));
      
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ results: largeDataset })
      });
    });
    
    const startTime = Date.now();
    await page.reload();
    await page.waitForSelector('[data-testid="business-ideas-list"]');
    
    const renderTime = Date.now() - startTime;
    expect(renderTime).toBeLessThan(3000); // 3 second budget for large datasets
  });
});
