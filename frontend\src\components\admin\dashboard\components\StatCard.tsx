import React from 'react';
import { ArrowUp, ArrowDown } from 'lucide-react';
import { useAppSelector } from '../../../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { RTLText } from '../../../common';

interface StatCardProps {
  title: string;
  count: number;
  icon: React.ReactNode;
  change: number;
  color: string;
}

/**
 * StatCard component displays a statistic with title, count, and change percentage
 */
const StatCard: React.FC<StatCardProps> = ({ title, count, icon, change, color  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language } = useAppSelector(state => state.language);

  return (
    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 hover:bg-white/15 transition-all duration-300 group">
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`p-3 rounded-xl ${color} group-hover:scale-110 transition-transform duration-300`}>
            {icon}
          </div>
          <div className={isRTL ? 'mr-4' : 'ml-4'}>
            <RTLText className="text-sm font-medium text-gray-300">{title}</RTLText>
            <RTLText as="h3" className="text-3xl font-bold text-white mt-1 group-hover:text-purple-200 transition-colors">
              {count.toLocaleString()}
            </RTLText>
          </div>
        </div>
        <div className={`flex flex-col items-end ${isRTL ? 'items-start' : 'items-end'}`}>
          <div className={`flex items-center text-sm font-medium ${isRTL ? 'flex-row-reverse' : ''}`}>
            <span className={`flex items-center px-2 py-1 rounded-full text-xs ${
              change >= 0
                ? "text-green-300 bg-green-500/20"
                : "text-red-300 bg-red-500/20"
            }`}>
              {change >= 0 ? (
                <ArrowUp size={12} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
              ) : (
                <ArrowDown size={12} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
              )}
              {Math.abs(change)}%
            </span>
          </div>
          <RTLText as="span" className="text-xs text-gray-400 mt-1">
            {t('admin.fromLastMonth', 'from last month')}
          </RTLText>
        </div>
      </div>

      {/* Progress bar */}
      <div className="mt-4">
        <div className="w-full glass-light rounded-full h-1.5">
          <div
            className={`h-1.5 rounded-full transition-all duration-1000 ${
              change >= 0 ? 'bg-gradient-to-r from-green-500 to-emerald-400' : 'bg-gradient-to-r from-red-500 to-pink-400'
            }`}
            style={{ width: `${Math.min(Math.abs(change) * 10, 100)}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default StatCard;
