#!/usr/bin/env python
import os
import sys
import django
from django.core.management import execute_from_command_line

# Set the settings module explicitly
os.environ['DJANGO_SETTINGS_MODULE'] = 'yasmeen_ai.settings'

# Setup Django
django.setup()

# Print the current settings module
from django.conf import settings
print(f"Using settings: {settings.SETTINGS_MODULE}")
print(f"ROOT_URLCONF: {settings.ROOT_URLCONF}")
print(f"DEBUG: {settings.DEBUG}")
print(f"INSTALLED_APPS: {settings.INSTALLED_APPS}")

# Check if the urls module can be imported
try:
    import yasmeen_ai.urls
    print("yasmeen_ai.urls imported successfully")
    print(f"URLs patterns: {yasmeen_ai.urls.urlpatterns}")
except Exception as e:
    print(f"Error importing yasmeen_ai.urls: {e}")

# Run the server
if __name__ == '__main__':
    execute_from_command_line(['manage.py', 'runserver'])