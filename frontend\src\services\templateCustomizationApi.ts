import { apiRequest } from './api';

export interface TemplateSectionDefinition {
  id: number;
  title: string;
  key: string;
  description: string;
  section_type: string;
  default_content: string;
  structure_definition: any;
  ai_prompt_template: string;
  is_system: boolean;
  created_at: string;
  updated_at: string;
}

export interface BusinessPlanTemplate {
  id: number;
  name: string;
  description: string;
  industry: string;
  template_type: string;
  template_type_display: string;
  sections: any;
  allows_customization: boolean;
  customization_options: any;

  // Real analytics fields from backend
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  estimated_time: number;
  usage_count: number;
  rating: number;
  rating_count: number;
  completion_rate: number;

  // Template properties from backend
  is_premium: boolean;
  is_featured: boolean;
  is_bestseller: boolean;
  tags: string[];
  icon: string;
  author: number | null;
  author_details?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };

  // Computed properties from backend
  popularity_score: number;
  is_new: boolean;

  // Metadata
  is_active: boolean;
  is_system: boolean;
  created_at: string;
  updated_at: string;

  // Additional fields for frontend compatibility (deprecated - use real fields above)
  category?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime?: number;
  popularity?: number;
  isNew?: boolean;
  isPremium?: boolean;
}

export interface CustomBusinessPlanTemplate {
  id: number;
  base_template: number;
  base_template_details?: BusinessPlanTemplate;
  name: string;
  description: string;
  owner: number;
  owner_details?: any;
  sections: any;
  custom_prompts: Record<string, string>;
  custom_instructions: Record<string, string>;
  is_public: boolean;
  shared_with: number[];
  shared_with_details?: any[];
  created_at: string;
  updated_at: string;
}

export const templateSectionDefinitionsAPI = {
  /**
   * Get all template section definitions
   * @param sectionType - Optional section type filter
   * @param isSystem - Optional system flag filter
   * @returns List of template section definitions
   */
  getSectionDefinitions: async (sectionType?: string, isSystem?: boolean) => {
    try {
      let url = '/incubator/template-sections/';
      const params = new URLSearchParams();

      if (sectionType) {
        params.append('section_type', sectionType);
      }

      if (isSystem !== undefined) {
        params.append('is_system', isSystem.toString());
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      return await apiRequest<TemplateSectionDefinition[]>(url);
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error fetching template section definitions:', error);
      throw error;
    }
  },

  /**
   * Get a specific template section definition
   * @param id - Section definition ID
   * @returns Template section definition
   */
  getSectionDefinition: async (id: number) => {
    try {
      return await apiRequest<TemplateSectionDefinition>(`/incubator/template-sections/${id}/`);
    } catch (error) {
      if (import.meta.env.DEV) console.error(`Error fetching template section definition ${id}:`, error);
      throw error;
    }
  }
};

export interface TemplateFilters {
  industry?: string;
  template_type?: string;
  allows_customization?: boolean;
  search?: string;
  difficulty?: string;
  category?: string;
}

export interface TemplateCategory {
  name: string;
  count: number;
  description: string;
}

export interface TemplateRating {
  rating: number;
  review?: string;
}

export interface TemplateAnalytics {
  usage_count: number;
  success_rate: number;
  average_completion_time: number;
  user_ratings: any[];
  monthly_usage: any[];
}

export const businessPlanTemplatesAPI = {
  /**
   * Get all business plan templates
   * @param filters - Optional filters for templates
   * @returns List of business plan templates
   */
  getTemplates: async (filters?: TemplateFilters): Promise<BusinessPlanTemplate[]> => {
    try {
      let url = '/incubator/business-plan-templates/';
      const params = new URLSearchParams();

      if (filters?.industry) {
        params.append('industry', filters.industry);
      }

      if (filters?.template_type) {
        params.append('template_type', filters.template_type);
      }

      if (filters?.allows_customization !== undefined) {
        params.append('allows_customization', filters.allows_customization.toString());
      }

      if (filters?.search) {
        params.append('search', filters.search);
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      // Make request without authentication for public template browsing
      const response = await apiRequest<any>(url, 'GET', undefined, false);

      // Handle paginated response format
      let templatesData: any[] = [];
      if (response && typeof response === 'object') {
        if (Array.isArray(response)) {
          // Direct array response
          templatesData = response;
        } else if (response.results && Array.isArray(response.results)) {
          // Paginated response with results array
          templatesData = response.results;
        } else {
                    return [];
        }
      } else {
                return [];
      }

      // Transform backend data to match frontend interface (minimal transformation now)
      const templates = templatesData.map((template: any) => ({
        ...template,
        // Add computed fields for backward compatibility
        category: template.industry?.toLowerCase().replace(/\s+/g, '_') || 'general',
        difficulty: template.difficulty_level, // Use real field
        estimatedTime: template.estimated_time, // Use real field
        popularity: template.popularity_score, // Use real field
        isNew: template.is_new, // Use real field
        isPremium: template.is_premium, // Use real field
        // All other fields come directly from the API now
      }));

      return templates;
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error fetching business plan templates:', error);
      // Return empty array instead of throwing error for public browsing
      return [];
    }
  },

  /**
   * Get a specific business plan template
   * @param id - Template ID
   * @returns Business plan template
   */
  getTemplate: async (id: number) => {
    try {
      // Make request without authentication for public template viewing
      return await apiRequest<BusinessPlanTemplate>(`/incubator/business-plan-templates/${id}/`, 'GET', undefined, false);
    } catch (error) {
      if (import.meta.env.DEV) console.error(`Error fetching business plan template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get customization options for a template
   * @param id - Template ID
   * @returns Customization options
   */
  getCustomizationOptions: async (id: number) => {
    try {
      return await apiRequest<any>(`/incubator/business-plan-templates/${id}/customization_options/`);
    } catch (error) {
      if (import.meta.env.DEV) console.error(`Error fetching customization options for template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get template categories with counts
   * @returns List of template categories with counts
   */
  getCategories: async () => {
    try {
      return await apiRequest<Array<{name: string, count: number, description: string}>>('/incubator/business-plan-templates/categories/', 'GET', undefined, false);
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error fetching template categories:', error);
      throw new Error('Failed to load template categories. Please try again later.');
    }
  },

  /**
   * Get user's favorite templates
   * @returns List of favorite template IDs
   */
  getUserFavorites: async (): Promise<number[]> => {
    try {
      const response = await apiRequest<{favorites: number[]}>('/incubator/business-plan-templates/favorites/');
      return response.favorites || [];
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error fetching user favorites:', error);
      return []; // Return empty array if not authenticated or error
    }
  },

  /**
   * Add template to user's favorites
   * @param templateId - Template ID to add to favorites
   */
  addToFavorites: async (templateId: number) => {
    try {
      return await apiRequest<{message: string}>('/incubator/business-plan-templates/favorites/', 'POST', { template_id: templateId });
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error adding to favorites:', error);
      throw new Error('Failed to add template to favorites. Please try again later.');
    }
  },

  /**
   * Remove template from user's favorites
   * @param templateId - Template ID to remove from favorites
   */
  removeFromFavorites: async (templateId: number) => {
    try {
      return await apiRequest<{message: string}>(`/incubator/business-plan-templates/favorites/${templateId}/`, 'DELETE');
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error removing from favorites:', error);
      throw new Error('Failed to remove template from favorites. Please try again later.');
    }
  },

  /**
   * Generate a template using AI based on industry
   * @param industry - Industry for template generation
   * @returns Generated template
   */
  generateTemplate: async (industry: string) => {
    try {
      return await apiRequest<any>('/incubator/business-plan-templates/generate_template/', 'POST', { industry });
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error generating AI template:', error);
      throw new Error(`Failed to generate template for ${industry} industry. Please try again later.`);
    }
  },

  /**
   * Get analytics for a specific template
   * @param id - Template ID
   * @returns Template analytics
   */
  getTemplateAnalytics: async (id: number): Promise<TemplateAnalytics> => {
    try {
      return await apiRequest<TemplateAnalytics>(`/incubator/business-plan-templates/${id}/analytics/`);
    } catch (error) {
      if (import.meta.env.DEV) console.error(`Error fetching analytics for template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Rate a template
   * @param id - Template ID
   * @param rating - Rating data
   * @returns Response message
   */
  rateTemplate: async (id: number, rating: TemplateRating) => {
    try {
      return await apiRequest<{ message: string }>(
        `/incubator/business-plan-templates/${id}/rate/`,
        'POST',
        rating
      );
    } catch (error) {
      if (import.meta.env.DEV) console.error(`Error rating template ${id}:`, error);
      throw error;
    }
  }
};

export const customTemplatesAPI = {
  /**
   * Get all custom templates accessible to the user
   * @returns List of custom templates
   */
  getCustomTemplates: async () => {
    try {
      // Custom templates are user-specific and require authentication
      const response = await apiRequest<any>('/incubator/custom-templates/', 'GET', undefined, true);

      // Handle paginated response format
      if (response && typeof response === 'object') {
        if (Array.isArray(response)) {
          // Direct array response
          return response;
        } else if (response.results && Array.isArray(response.results)) {
          // Paginated response with results array
          return response.results;
        } else {
                    return [];
        }
      } else {
                return [];
      }
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error fetching custom templates:', error);

      // Return empty array when API is not available
      return [];
    }
  },

  /**
   * Get a specific custom template
   * @param id - Custom template ID
   * @returns Custom template
   */
  getCustomTemplate: async (id: number) => {
    try {
      return await apiRequest<CustomBusinessPlanTemplate>(`/incubator/custom-templates/${id}/`);
    } catch (error) {
      if (import.meta.env.DEV) console.error(`Error fetching custom template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create a new custom template
   * @param data - Custom template data
   * @returns Created custom template
   */
  createCustomTemplate: async (data: Partial<CustomBusinessPlanTemplate>) => {
    try {
      // Ensure required fields are present
      const templateData = {
        ...data,
        // Remove hardcoded owner - let backend set it from authenticated user
        name: data.name || 'Custom Template',
        description: data.description || 'Custom business plan template',
        is_public: data.is_public || false,
        customization_options: data.customization_options || {}
      };

      // Only include base_template if it's provided and valid
      if (data.base_template && data.base_template > 0) {
        templateData.base_template = data.base_template;
      }

      // Custom template creation requires authentication
      return await apiRequest<CustomBusinessPlanTemplate>('/incubator/custom-templates/', 'POST', templateData, true);
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error creating custom template:', error);
      throw new Error('Failed to create custom template. Please try again later.');
    }
  },

  /**
   * Update a custom template
   * @param id - Custom template ID
   * @param data - Updated custom template data
   * @returns Updated custom template
   */
  updateCustomTemplate: async (id: number, data: Partial<CustomBusinessPlanTemplate>) => {
    try {
      return await apiRequest<CustomBusinessPlanTemplate>(`/incubator/custom-templates/${id}/`, 'PUT', data);
    } catch (error) {
      if (import.meta.env.DEV) console.error(`Error updating custom template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a custom template
   * @param id - Custom template ID
   */
  deleteCustomTemplate: async (id: number) => {
    try {
      await apiRequest(`/incubator/custom-templates/${id}/`, 'DELETE');
    } catch (error) {
      if (import.meta.env.DEV) console.error(`Error deleting custom template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create a template (alias for createCustomTemplate for backward compatibility)
   * @param data - Template data
   * @returns Created template
   */
  createTemplate: async (data: Partial<CustomBusinessPlanTemplate>) => {
    return customTemplatesAPI.createCustomTemplate(data);
  },

  /**
   * Share a custom template with other users
   * @param id - Custom template ID
   * @param userIds - User IDs to share with
   * @returns Response message
   */
  shareTemplate: async (id: number, userIds: number[]) => {
    try {
      return await apiRequest<{ message: string }>(
        `/incubator/custom-templates/${id}/share/`,
        'POST',
        { user_ids: userIds }
      );
    } catch (error) {
      if (import.meta.env.DEV) console.error(`Error sharing custom template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Unshare a custom template with users
   * @param id - Custom template ID
   * @param userIds - User IDs to unshare with
   * @returns Response message
   */
  unshareTemplate: async (id: number, userIds: number[]) => {
    try {
      return await apiRequest<{ message: string }>(
        `/incubator/custom-templates/${id}/unshare/`,
        'POST',
        { user_ids: userIds }
      );
    } catch (error) {
      if (import.meta.env.DEV) console.error(`Error unsharing custom template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Make a custom template public
   * @param id - Custom template ID
   * @returns Response message
   */
  makePublic: async (id: number) => {
    try {
      return await apiRequest<{ message: string }>(
        `/incubator/custom-templates/${id}/make_public/`,
        'POST'
      );
    } catch (error) {
      if (import.meta.env.DEV) console.error(`Error making custom template ${id} public:`, error);
      throw error;
    }
  },

  /**
   * Make a custom template private
   * @param id - Custom template ID
   * @returns Response message
   */
  makePrivate: async (id: number) => {
    try {
      return await apiRequest<{ message: string }>(
        `/incubator/custom-templates/${id}/make_private/`,
        'POST'
      );
    } catch (error) {
      if (import.meta.env.DEV) console.error(`Error making custom template ${id} private:`, error);
      throw error;
    }
  },

  /**
   * Get template collaborators
   */
  getTemplateCollaborators: async (templateId: number) => {
    try {
      return await apiRequest<any[]>(`/incubator/custom-templates/${templateId}/collaborators/`);
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error fetching template collaborators:', error);
      return [];
    }
  },

  /**
   * Get template comments/feedback
   */
  getTemplateComments: async (templateId: number) => {
    try {
      return await apiRequest<any[]>(`/incubator/custom-templates/${templateId}/comments/`);
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error fetching template comments:', error);
      return [];
    }
  },

  /**
   * Get template versions/history
   */
  getTemplateVersions: async (templateId: number) => {
    try {
      return await apiRequest<any[]>(`/incubator/custom-templates/${templateId}/versions/`);
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error fetching template versions:', error);
      return [];
    }
  },

  /**
   * Get automation rules for template
   */
  getAutomationRules: async (templateId: number) => {
    try {
      return await apiRequest<any[]>(`/incubator/custom-templates/${templateId}/automation-rules/`);
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error fetching automation rules:', error);
      return [];
    }
  },

  /**
   * Get workflow templates
   */
  getWorkflowTemplates: async (templateId: number) => {
    try {
      return await apiRequest<any[]>(`/incubator/custom-templates/${templateId}/workflows/`);
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error fetching workflow templates:', error);
      return [];
    }
  },

  /**
   * Toggle automation rule status
   */
  toggleAutomationRule: async (ruleId: number) => {
    try {
      return await apiRequest<any>(`/incubator/automation-rules/${ruleId}/toggle/`, 'POST');
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error toggling automation rule:', error);
      throw error;
    }
  }
};

// Helper functions for data transformation
export const mapTemplateDifficulty = (templateType: string): 'beginner' | 'intermediate' | 'advanced' => {
  const difficultyMap: Record<string, 'beginner' | 'intermediate' | 'advanced'> = {
    'standard': 'beginner',
    'lean': 'beginner',
    'saas': 'intermediate',
    'ecommerce': 'intermediate',
    'restaurant': 'intermediate',
    'consulting': 'beginner',
    'mobile_app': 'advanced',
    'nonprofit': 'intermediate',
    'fintech': 'advanced',
    'healthcare': 'intermediate',
    'manufacturing': 'advanced',
    'ai_startup': 'advanced',
    'blockchain': 'advanced',
    'gaming': 'intermediate'
  };
  return difficultyMap[templateType] || 'beginner';
};

// Note: Template time estimation and popularity calculation are now handled by the backend
// These functions are kept for backward compatibility but should use real data from API

export const estimateTemplateTime = (templateType: string): number => {
    // Fallback values only - real data should come from API
  const timeMap: Record<string, number> = {
    'standard': 8,
    'lean': 2,
    'saas': 12,
    'ecommerce': 10,
    'restaurant': 6,
    'consulting': 4,
    'mobile_app': 15,
    'nonprofit': 8,
    'fintech': 12,
    'healthcare': 10,
    'manufacturing': 14,
    'ai_startup': 16,
    'blockchain': 18,
    'gaming': 12
  };
  return timeMap[templateType] || 6;
};

export const calculatePopularity = (template: any): number => {
    // Return the real popularity score from API if available
  return template.popularity_score || 0;
};

export const getTemplateIcon = (templateType: string): string => {
  const iconMap: Record<string, string> = {
    'standard': 'FileText',
    'lean': 'Zap',
    'saas': 'Monitor',
    'ecommerce': 'ShoppingCart',
    'restaurant': 'Utensils',
    'consulting': 'Users',
    'mobile_app': 'Smartphone',
    'nonprofit': 'Heart',
    'fintech': 'DollarSign',
    'healthcare': 'Heart',
    'manufacturing': 'Wrench',
    'ai_startup': 'Brain',
    'blockchain': 'Link',
    'gaming': 'Gamepad2'
  };
  return iconMap[templateType] || 'FileText';
};

export const isNewTemplate = (createdAt: string): boolean => {
  const createdDate = new Date(createdAt);
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  return createdDate > thirtyDaysAgo;
};

export const isPremiumTemplate = (templateType: string): boolean => {
  const premiumTypes = ['saas', 'mobile_app', 'fintech', 'ai_startup', 'blockchain'];
  return premiumTypes.includes(templateType);
};

export const generateTemplateTags = (template: any): string[] => {
  const tags = [];

  if (template.industry) {
    tags.push(template.industry);
  }

  if (template.template_type) {
    tags.push(template.template_type.replace('_', ' '));
  }

  if (isPremiumTemplate(template.template_type)) {
    tags.push('Premium');
  }

  if (isNewTemplate(template.created_at)) {
    tags.push('New');
  }

  return tags;
};
