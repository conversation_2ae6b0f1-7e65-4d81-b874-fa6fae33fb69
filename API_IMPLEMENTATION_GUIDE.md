# 🛠️ API IMPLEMENTATION GUIDE

## 📋 IMPLEMENTATION CHECKLIST

### 🔴 CRITICAL FIXES NEEDED

#### 1. USER ACTIVITY API (`/api/users/users/user_activity/`)

**Backend Implementation Required:**
```python
# In backend/users/views.py
@action(detail=False, methods=['get'])
def user_activity(self, request):
    """Get user activity statistics"""
    user = request.user
    
    # Calculate user activity metrics
    from django.utils import timezone
    from datetime import timedelta
    
    # Get user's business ideas
    business_ideas = BusinessIdea.objects.filter(owner=user)
    
    # Get user's forum activity
    forum_posts = ForumPost.objects.filter(author=user)
    
    # Calculate time spent (mock for now)
    activity_data = {
        'total_sessions': 25,
        'time_spent_minutes': 1440,
        'last_login': user.last_login.isoformat() if user.last_login else None,
        'business_ideas_created': business_ideas.count(),
        'plans_completed': business_ideas.filter(status='completed').count(),
        'forum_posts': forum_posts.count(),
        'reputation_score': 150  # Calculate from forum reputation
    }
    
    return Response(activity_data)
```

#### 2. FORUM ACTIVITY API (`/api/users/users/forum_activity/`)

**Backend Implementation Required:**
```python
@action(detail=False, methods=['get'])
def forum_activity(self, request):
    """Get user's forum activity"""
    user = request.user
    
    # Get forum statistics
    posts = ForumPost.objects.filter(author=user)
    threads = ForumThread.objects.filter(author=user)
    
    activity_data = {
        'posts_count': posts.count(),
        'threads_created': threads.count(),
        'reputation_earned': 45,  # Calculate from UserReputation
        'helpful_answers': 8,     # Calculate from post ratings
        'recent_posts': []        # Serialize recent posts
    }
    
    return Response(activity_data)
```

#### 3. TEMPLATE CREATION FIX

**Issue:** Backend endpoint exists but not handling requests properly

**Fix Required in:** `backend/incubator/views_template_customization.py`

```python
# In CustomBusinessPlanTemplateViewSet
def create(self, request, *args, **kwargs):
    """Create custom template"""
    try:
        # Get request data
        data = request.data
        
        # Create template
        template = CustomBusinessPlanTemplate.objects.create(
            name=data.get('name'),
            description=data.get('description'),
            category=data.get('category'),
            created_by=request.user,
            is_active=True
        )
        
        # Create default sections
        default_sections = [
            {'title': 'Executive Summary', 'order': 1},
            {'title': 'Market Analysis', 'order': 2},
            {'title': 'Financial Projections', 'order': 3}
        ]
        
        for section_data in default_sections:
            TemplateSectionDefinition.objects.create(
                template=template,
                **section_data
            )
        
        serializer = self.get_serializer(template)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        return Response(
            {'error': str(e)}, 
            status=status.HTTP_400_BAD_REQUEST
        )
```

#### 4. AUTHENTICATION FIXES

**Issue:** 401 errors for authenticated users

**Fix Required in:** `backend/users/auth_views.py`

```python
# Add proper token validation
class UserView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get current user data"""
        try:
            user = request.user
            if not user.is_authenticated:
                return Response(
                    {'error': 'Authentication required'}, 
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser
            }
            
            return Response(user_data)
            
        except Exception as e:
            return Response(
                {'error': 'Failed to get user data'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
```

---

## 🔧 FRONTEND FIXES NEEDED

### 1. BETTER ERROR HANDLING

**File:** `frontend/src/services/api.ts`

```typescript
// Add better error handling to apiRequest function
export async function apiRequest<T>(endpoint: string, method: string = 'GET', data?: any): Promise<T> {
  const token = getAuthToken();
  
  const config: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  };

  if (data && method !== 'GET') {
    config.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(`${API_URL}${endpoint}`, config);
    
    // Handle authentication errors
    if (response.status === 401) {
      console.error('Authentication failed, clearing tokens');
      clearAuthTokens();
      window.location.href = '/login';
      throw new ApiError('Authentication required', 401);
    }
    
    // Handle other errors
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new ApiError(
        errorData.message || `HTTP ${response.status}`, 
        response.status, 
        errorData
      );
    }

    return await response.json();
  } catch (error) {
    console.error(`API Error [${method} ${endpoint}]:`, error);
    throw error;
  }
}
```

### 2. FALLBACK DATA FOR LOADING STATES

**File:** `frontend/src/components/dashboard/user-dashboard/UserDashboard.tsx`

```typescript
// Add fallback data when APIs fail
const fetchDashboardData = async () => {
  if (!user) return;

  setDashboardData(prev => ({ ...prev, loading: true, error: null }));

  try {
    const [businessIdeas, userActivity] = await Promise.all([
      businessIdeasAPI.getBusinessIdeas(),
      userAPI.getUserActivity()
    ]);

    // Process data...
    setDashboardData({
      businessIdeas: userBusinessIdeas,
      userActivity,
      loading: false,
      error: null
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    
    // Provide fallback data instead of just error
    setDashboardData({
      businessIdeas: [],
      userActivity: {
        total_sessions: 0,
        time_spent_minutes: 0,
        business_ideas_created: 0,
        plans_completed: 0,
        forum_posts: 0,
        reputation_score: 0
      },
      loading: false,
      error: 'Some data may be unavailable. Please try refreshing.'
    });
  }
};
```

---

## 📊 TESTING STRATEGY

### 1. API ENDPOINT TESTING
```bash
# Test user activity endpoint
curl -H "Authorization: Bearer <token>" \
     http://localhost:8000/api/users/users/user_activity/

# Test template creation
curl -X POST \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"name":"Test Template","description":"Test","category":"general"}' \
     http://localhost:8000/api/incubator/custom-templates/
```

### 2. FRONTEND INTEGRATION TESTING
- Test dashboard loading states
- Verify error handling
- Check fallback data display
- Validate authentication flows

---

## 🎯 IMPLEMENTATION ORDER

1. **Day 1:** Fix authentication and user activity APIs
2. **Day 2:** Fix template creation and forum data
3. **Day 3:** Add error handling and fallback data
4. **Day 4:** Test all fixes end-to-end
5. **Day 5:** Documentation and cleanup

---

**Next Task:** Implement authentication fixes and user activity APIs
