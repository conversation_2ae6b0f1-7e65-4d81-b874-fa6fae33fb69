import { chromium, FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test setup...');
  
  const { baseURL } = config.projects[0].use;
  
  try {
    // Create test results directory
    const testResultsDir = 'test-results';
    if (!fs.existsSync(testResultsDir)) {
      fs.mkdirSync(testResultsDir, { recursive: true });
    }
    
    // Setup authentication if needed
    await setupAuthentication(baseURL);
    
    console.log('✅ E2E test setup completed successfully');
    
  } catch (error) {
    console.error('❌ E2E test setup failed:', error);
    throw error;
  }
}

async function setupAuthentication(baseURL?: string) {
  try {
    console.log('🔐 Setting up authentication...');
    
    // For now, just create empty auth files
    // In a real scenario, this would authenticate and save session
    const authFile = 'test-results/admin-auth.json';
    fs.writeFileSync(authFile, JSON.stringify({ authenticated: false }));
    
    console.log('🔐 Authentication setup completed');
    
  } catch (error) {
    console.warn('⚠️ Could not setup authentication:', error.message);
  }
}

export default globalSetup;
