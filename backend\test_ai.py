#!/usr/bin/env python
"""
Test script for AI functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings_minimal')
django.setup()

def test_ai_service():
    print("🤖 Testing AI Service...")

    # Test basic import
    try:
        from incubator.ai_service_simple import is_ai_available, generate_business_analysis
        print("✅ AI service imported successfully")
    except Exception as e:
        print(f"❌ Failed to import AI service: {e}")
        return

    # Test availability
    try:
        available = is_ai_available()
        print(f"AI Available: {available}")
    except Exception as e:
        print(f"❌ Error checking AI availability: {e}")
        return
    
    if not available:
        print("❌ AI service is not available")
        return
    
    # Test business analysis
    test_business_idea = {
        'title': 'AI-Powered Language Learning Platform',
        'description': 'An innovative platform that personalizes language learning',
        'problem_statement': 'Traditional language learning is one-size-fits-all',
        'solution_description': 'AI adapts to individual learning styles',
        'target_audience': 'Language learners of all ages',
        'market_opportunity': 'Growing demand for personalized education',
        'business_model': 'Subscription-based with premium features',
        'current_stage': 'concept'
    }
    
    print("\n📊 Testing business analysis...")
    analysis = generate_business_analysis(test_business_idea)
    
    if analysis:
        print("✅ AI Analysis generated successfully!")
        print(f"Analysis preview: {analysis[:200]}...")
    else:
        print("❌ Failed to generate AI analysis")

if __name__ == "__main__":
    test_ai_service()
