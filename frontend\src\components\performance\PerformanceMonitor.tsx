import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
/**
 * Performance Monitor Component
 * Monitors and optimizes app performance
 */

import React, { useEffect, useState, useCallback } from 'react';
import { Activity, Zap, AlertTriangle, CheckCircle } from 'lucide-react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  bundleSize: number;
  apiResponseTime: number;
  errorRate: number;
}

interface PerformanceIssue {
  type: 'warning' | 'error' | 'info';
  message: string;
  suggestion: string;
  timestamp: number;
}

export const PerformanceMonitor: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    bundleSize: 0,
    apiResponseTime: 0,
    errorRate: 0
  });
  
  const [issues, setIssues] = useState<PerformanceIssue[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  // Performance monitoring functions
  const measureLoadTime = useCallback(() => {
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      return navigation.loadEventEnd - navigation.fetchStart;
    }
    return 0;
  }, []);

  const measureMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize / 1024 / 1024; // Convert to MB
    }
    return 0;
  }, []);

  const measureRenderTime = useCallback(() => {
    return performance.now();
  }, []);

  // Check for performance issues
  const checkPerformanceIssues = useCallback((currentMetrics: PerformanceMetrics) => {
    const newIssues: PerformanceIssue[] = [];
    const now = Date.now();

    // Check load time
    if (currentMetrics.loadTime > 3000) {
      newIssues.push({
        type: 'warning',
        message: t("common.slow.page.load", "Slow page load time detected"),
        suggestion: 'Consider code splitting and lazy loading',
        timestamp: now
      });
    }

    // Check memory usage
    if (currentMetrics.memoryUsage > 50) {
      newIssues.push({
        type: 'error',
        message: t("common.high.memory.usage", "High memory usage detected"),
        suggestion: 'Check for memory leaks and optimize components',
        timestamp: now
      });
    }

    // Check API response time
    if (currentMetrics.apiResponseTime > 2000) {
      newIssues.push({
        type: 'warning',
        message: t("common.slow.api.response", "Slow API response time"),
        suggestion: 'Optimize backend queries and consider caching',
        timestamp: now
      });
    }

    // Check error rate
    if (currentMetrics.errorRate > 0.05) {
      newIssues.push({
        type: 'error',
        message: 'High error rate detected',
        suggestion: 'Review error logs and fix critical issues',
        timestamp: now
      });
    }

    setIssues(prev => [...prev.slice(-10), ...newIssues]); // Keep last 10 issues
  }, []);

  // Update metrics
  const updateMetrics = useCallback(() => {
    const newMetrics: PerformanceMetrics = {
      loadTime: measureLoadTime(),
      renderTime: measureRenderTime(),
      memoryUsage: measureMemoryUsage(),
      bundleSize: 0, // Would need to be calculated during build
      apiResponseTime: 0, // Would be updated by API calls
      errorRate: 0 // Would be tracked by error boundary
    };

    setMetrics(newMetrics);
    checkPerformanceIssues(newMetrics);
  }, [measureLoadTime, measureRenderTime, measureMemoryUsage, checkPerformanceIssues]);

  // Start/stop monitoring
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isMonitoring) {
      updateMetrics(); // Initial measurement
      interval = setInterval(updateMetrics, 5000); // Update every 5 seconds
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isMonitoring, updateMetrics]);

  // Performance optimization suggestions
  const getOptimizationSuggestions = () => {
    const suggestions = [];

    if (metrics.loadTime > 2000) {
      suggestions.push(t("common.implement.code.splitting", "Implement code splitting for faster initial load"));
    }
    if (metrics.memoryUsage > 30) {
      suggestions.push('Use React.memo() for expensive components');
    }
    if (metrics.apiResponseTime > 1000) {
      suggestions.push(t("common.implement.request.caching", "Implement request caching and pagination"));
    }

    return suggestions;
  };

  const getPerformanceScore = () => {
    let score = 100;
    
    if (metrics.loadTime > 3000) score -= 20;
    else if (metrics.loadTime > 2000) score -= 10;
    
    if (metrics.memoryUsage > 50) score -= 25;
    else if (metrics.memoryUsage > 30) score -= 15;
    
    if (metrics.apiResponseTime > 2000) score -= 20;
    else if (metrics.apiResponseTime > 1000) score -= 10;
    
    if (metrics.errorRate > 0.05) score -= 30;
    else if (metrics.errorRate > 0.02) score -= 15;

    return Math.max(0, score);
  };

  const performanceScore = getPerformanceScore();
  const suggestions = getOptimizationSuggestions();

  if (process.env.NODE_ENV === 'production') {
    return null; // Don't show in production
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900/95 backdrop-blur-sm rounded-lg border border-gray-700 p-4 max-w-sm z-50">
      {/* Header */}
      <div className={`flex items-center justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <Activity size={16} className="text-blue-400" />
          <span className="text-sm font-medium text-white">{t("common.performance", "Performance")}</span>
        </div>
        <button
          onClick={() => setIsMonitoring(!isMonitoring)}
          className={`px-2 py-1 rounded text-xs ${
            isMonitoring 
              ? 'bg-green-600 text-white' 
              : 'bg-gray-600 text-gray-300'}
          }`}
        >
          {isMonitoring ? t("common.on", "ON") : t("common.off", "OFF")}
        </button>
      </div>

      {/* Performance Score */}
      <div className="mb-3">
        <div className={`flex items-center justify-between mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
          <span className="text-xs text-gray-400">{t("common.score", "Score")}</span>
          <span className={`text-sm font-bold ${
            performanceScore >= 80 ? 'text-green-400' :
            performanceScore >= 60 ? 'text-yellow-400' : 'text-red-400'}
          }`}>
            {performanceScore}/100
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              performanceScore >= 80 ? 'bg-green-500' :
              performanceScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'}
            }`}
            style={{ width: `${performanceScore}%` }}
          />
        </div>
      </div>

      {/* Metrics */}
      {isMonitoring && (
        <div className="space-y-2 mb-3">
          <div className={`flex justify-between text-xs ${isRTL ? "flex-row-reverse" : ""}`}>
            <span className="text-gray-400">{t("common.load.time", "Load Time")}</span>
            <span className="text-white">{metrics.loadTime.toFixed(0)}ms</span>
          </div>
          <div className={`flex justify-between text-xs ${isRTL ? "flex-row-reverse" : ""}`}>
            <span className="text-gray-400">{t("common.memory", "Memory")}</span>
            <span className="text-white">{metrics.memoryUsage.toFixed(1)}MB</span>
          </div>
          <div className={`flex justify-between text-xs ${isRTL ? "flex-row-reverse" : ""}`}>
            <span className="text-gray-400">{t("common.api.response", "API Response")}</span>
            <span className="text-white">{metrics.apiResponseTime.toFixed(0)}ms</span>
          </div>
        </div>
      )}

      {/* Recent Issues */}
      {issues.length > 0 && (
        <div className="mb-3">
          <div className="text-xs text-gray-400 mb-1">{t("common.recent.issues", "Recent Issues")}</div>
          <div className="space-y-1 max-h-20 overflow-y-auto">
            {issues.slice(-3).map((issue, index) => (
              <div key={index} className={`flex items-start space-x-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                {issue.type === 'error' ? (
                  <AlertTriangle size={12} className="text-red-400 mt-0.5" />
                ) : (
                  <AlertTriangle size={12} className="text-yellow-400 mt-0.5" />
                )}
                <span className="text-xs text-gray-300">{issue.message}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Suggestions */}
      {suggestions.length > 0 && (
        <div>
          <div className="text-xs text-gray-400 mb-1">t("common.suggestions", "Suggestions")</div>
          <div className="space-y-1">
            {suggestions.slice(0, 2).map((suggestion, index) => (
              <div key={index} className={`flex items-start space-x-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                <Zap size={12} className="text-blue-400 mt-0.5" />
                <span className="text-xs text-gray-300">{suggestion}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
