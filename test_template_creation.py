#!/usr/bin/env python3
"""
Test script to verify template creation API fixes
"""
import requests
import json

# API base URL
BASE_URL = "http://localhost:8000/api"

def test_template_endpoints():
    """Test template-related endpoints"""
    print("🔍 Testing Template Endpoints...")
    
    # Test login first
    login_url = f"{BASE_URL}/auth/token/"
    login_data = {
        "username": "superadmin",
        "password": "superadmin123"
    }
    
    try:
        print(f"📡 Testing POST {login_url}")
        response = requests.post(login_url, json=login_data)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            access_token = data.get('access')
            print(f"🔑 Access token received: {access_token[:20]}...")
        else:
            print(f"❌ Login failed: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server. Is it running on port 8000?")
        return None
    except Exception as e:
        print(f"❌ Login test failed: {e}")
        return None

    headers = {"Authorization": f"Bearer {access_token}"}

    # Test business plan templates list
    print("\n🔍 Testing Business Plan Templates List...")
    templates_url = f"{BASE_URL}/incubator/business-plan-templates/"
    
    try:
        print(f"📡 Testing GET {templates_url}")
        response = requests.get(templates_url)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Templates endpoint working!")
            if isinstance(data, list):
                print(f"📊 Found {len(data)} templates")
            elif isinstance(data, dict) and 'results' in data:
                print(f"📊 Found {len(data['results'])} templates (paginated)")
            else:
                print(f"📊 Response format: {type(data)}")
        else:
            print(f"❌ Templates list failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Templates list test failed: {e}")

    # Test template categories
    print("\n🔍 Testing Template Categories...")
    categories_url = f"{BASE_URL}/incubator/business-plan-templates/categories/"
    
    try:
        print(f"📡 Testing GET {categories_url}")
        response = requests.get(categories_url)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Categories endpoint working!")
            print(f"📊 Found {len(data)} categories")
            for category in data[:3]:  # Show first 3 categories
                print(f"  - {category.get('name', 'Unknown')}: {category.get('count', 0)} templates")
        else:
            print(f"❌ Categories failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Categories test failed: {e}")

    # Test custom template creation
    print("\n🔍 Testing Custom Template Creation...")
    custom_templates_url = f"{BASE_URL}/incubator/custom-templates/"
    
    template_data = {
        "name": "Test Custom Template",
        "description": "A test template created via API",
        "sections": {
            "executive_summary": {
                "title": "Executive Summary",
                "description": "Overview of the business",
                "order": 1,
                "required": True
            },
            "market_analysis": {
                "title": "Market Analysis",
                "description": "Analysis of the target market",
                "order": 2,
                "required": True
            }
        },
        "is_public": False
    }
    
    try:
        print(f"📡 Testing POST {custom_templates_url}")
        response = requests.post(custom_templates_url, json=template_data, headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print(f"✅ Custom template creation working!")
            print(f"📊 Created template ID: {data.get('id')}")
            print(f"📊 Template name: {data.get('name')}")
            return data.get('id')
        else:
            print(f"❌ Custom template creation failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Custom template creation test failed: {e}")
        return None

def main():
    """Main test function"""
    print("🚀 Starting Template Creation Tests...")
    print("=" * 50)
    
    test_template_endpoints()
    
    print("\n" + "=" * 50)
    print("🏁 Template Creation Tests Complete!")

if __name__ == "__main__":
    main()
