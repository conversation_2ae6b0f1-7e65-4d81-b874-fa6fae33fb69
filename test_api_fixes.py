#!/usr/bin/env python3
"""
Test script to verify API fixes
"""
import requests
import json

# API base URL
BASE_URL = "http://localhost:8000/api"

def test_auth_endpoints():
    """Test authentication endpoints"""
    print("🔍 Testing Authentication Endpoints...")
    
    # Test login endpoint
    login_url = f"{BASE_URL}/auth/token/"
    login_data = {
        "username": "superadmin",
        "password": "superadmin123"
    }
    
    try:
        print(f"📡 Testing POST {login_url}")
        response = requests.post(login_url, json=login_data)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Login endpoint working!")
            data = response.json()
            access_token = data.get('access')
            print(f"🔑 Access token received: {access_token[:20]}...")
            return access_token
        else:
            print(f"❌ Login failed: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server. Is it running on port 8000?")
        return None
    except Exception as e:
        print(f"❌ Login test failed: {e}")
        return None

def test_user_activity_endpoint(token):
    """Test user activity endpoint"""
    if not token:
        print("⏭️ Skipping user activity test - no token")
        return
        
    print("\n🔍 Testing User Activity Endpoint...")
    
    url = f"{BASE_URL}/users/users/user_activity/"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        print(f"📡 Testing GET {url}")
        response = requests.get(url, headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ User activity endpoint working!")
            data = response.json()
            print(f"📊 Activity data keys: {list(data.keys())}")
        else:
            print(f"❌ User activity failed: {response.text}")
            
    except Exception as e:
        print(f"❌ User activity test failed: {e}")

def test_forum_activity_endpoint(token):
    """Test forum activity endpoint"""
    if not token:
        print("⏭️ Skipping forum activity test - no token")
        return
        
    print("\n🔍 Testing Forum Activity Endpoint...")
    
    url = f"{BASE_URL}/users/users/forum_activity/"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        print(f"📡 Testing GET {url}")
        response = requests.get(url, headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Forum activity endpoint working!")
            data = response.json()
            print(f"📊 Forum data keys: {list(data.keys())}")
        else:
            print(f"❌ Forum activity failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Forum activity test failed: {e}")

def test_current_user_endpoint(token):
    """Test current user endpoint"""
    if not token:
        print("⏭️ Skipping current user test - no token")
        return
        
    print("\n🔍 Testing Current User Endpoint...")
    
    url = f"{BASE_URL}/auth/user/"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        print(f"📡 Testing GET {url}")
        response = requests.get(url, headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Current user endpoint working!")
            data = response.json()
            print(f"👤 User: {data.get('username')} (ID: {data.get('id')})")
        else:
            print(f"❌ Current user failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Current user test failed: {e}")

def main():
    """Main test function"""
    print("🚀 Starting API Fix Tests...")
    print("=" * 50)
    
    # Test authentication
    token = test_auth_endpoints()
    
    # Test authenticated endpoints
    test_current_user_endpoint(token)
    test_user_activity_endpoint(token)
    test_forum_activity_endpoint(token)
    
    print("\n" + "=" * 50)
    print("🏁 API Fix Tests Complete!")

if __name__ == "__main__":
    main()
