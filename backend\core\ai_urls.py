"""
Centralized AI URLs
Universal AI endpoints that replace all duplicate AI URLs across the application
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
# Temporarily simplified imports to fix loading issue
try:
    from .views import AIConfigurationViewSet, AIStatusView
    from .ai_views import (
        UniversalChatView,
        universal_chat,
        universal_ai_status,
        universal_ai_test,
        AutomaticAIView
    )
    IMPORTS_SUCCESS = True
except ImportError as e:
    print(f"AI imports failed: {e}")
    # Create minimal fallback views
    from rest_framework.views import APIView
    from rest_framework.response import Response
    from django.http import JsonResponse

    class UniversalChatView(APIView):
        def post(self, request):
            return Response({"error": "AI service temporarily unavailable", "status": "offline"})

    class AutomaticAIView(APIView):
        def get(self, request):
            return Response({"error": "AI service temporarily unavailable", "status": "offline"})
        def post(self, request):
            return Response({"error": "AI service temporarily unavailable", "status": "offline"})

    def universal_chat(request):
        return JsonResponse({"error": "AI service temporarily unavailable", "status": "offline"})

    def universal_ai_status(request):
        return JsonResponse({"status": "offline", "message": "AI service temporarily unavailable"})

    def universal_ai_test(request):
        return JsonResponse({"status": "offline", "test": "failed"})

    AIConfigurationViewSet = None
    AIStatusView = UniversalChatView
    IMPORTS_SUCCESS = False

# Create router for AI configuration management
router = DefaultRouter()
if IMPORTS_SUCCESS and AIConfigurationViewSet:
    router.register(r'config', AIConfigurationViewSet, basename='ai-config')

# Simplified Universal AI URL patterns - essential endpoints only
urlpatterns = [
    # Configuration endpoints (if available)
    path('', include(router.urls)),
    path('status/', AIStatusView.as_view(), name='ai-status'),

    # Core AI functionality - essential endpoints
    path('chat/', UniversalChatView.as_view(), name='universal-chat'),
    path('chat/', universal_chat, name='ai-chat'),  # Function-based fallback
    path('status/', universal_ai_status, name='ai-status-func'),
    path('test/', universal_ai_test, name='ai-test'),

    # Simple access endpoints
    path('ask/', universal_chat, name='ai-ask'),
    path('health/', universal_ai_status, name='ai-health'),
    path('ping/', universal_ai_test, name='ai-ping'),

    # Automatic AI endpoints
    path('automatic/start/', AutomaticAIView.as_view(), {'action': 'start'}, name='ai-automatic-start'),
    path('automatic/stop/', AutomaticAIView.as_view(), {'action': 'stop'}, name='ai-automatic-stop'),
    path('automatic/status/', AutomaticAIView.as_view(), name='ai-automatic-status'),
    path('automatic/trigger/', AutomaticAIView.as_view(), {'action': 'trigger'}, name='ai-automatic-trigger'),
]


