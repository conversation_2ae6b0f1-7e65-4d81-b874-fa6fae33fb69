"""
Custom authentication views that include user data with JWT tokens
"""
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import get_user_model

User = get_user_model()


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """Custom serializer that includes user data with tokens"""
    
    def validate(self, attrs):
        data = super().validate(attrs)
        
        # Add user data to the response
        user = self.user
        data['user'] = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_admin': user.is_staff,
            'is_superuser': user.is_superuser,
        }
        
        # Add user profile data if it exists
        if hasattr(user, 'profile'):
            try:
                profile = user.profile
                data['user']['profile'] = {
                    'role': getattr(profile, 'role', 'user'),
                    'bio': getattr(profile, 'bio', ''),
                    'location': getattr(profile, 'location', ''),
                    'website': getattr(profile, 'website', ''),
                    'avatar': getattr(profile, 'avatar', None),
                }
            except:
                # Profile doesn't exist or has issues
                data['user']['profile'] = {
                    'role': 'user',
                    'bio': '',
                    'location': '',
                    'website': '',
                    'avatar': None,
                }
        else:
            # No profile model
            data['user']['profile'] = {
                'role': 'user',
                'bio': '',
                'location': '',
                'website': '',
                'avatar': None,
            }
        
        return data


class CustomTokenObtainPairView(TokenObtainPairView):
    """Custom token view that includes user data"""
    serializer_class = CustomTokenObtainPairSerializer
    
    def post(self, request, *args, **kwargs):
        try:
            response = super().post(request, *args, **kwargs)
            if response.status_code == 200:
                print(f"✅ Login successful for user: {response.data.get('user', {}).get('username', 'unknown')}")
            return response
        except Exception as e:
            print(f"❌ Login failed: {e}")
            return Response(
                {'detail': 'Invalid credentials'}, 
                status=status.HTTP_401_UNAUTHORIZED
            )
