"""
Minimal incubator URLs for basic CRUD functionality
Only includes essential endpoints without problematic dependencies
"""
from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from .views_minimal import (
    SimpleBusinessIdeaViewSet, SimpleProgressUpdateViewSet
)
from .views_template_analytics import TemplateAnalyticsViewSet

router = DefaultRouter()

# Core business functionality - minimal versions
router.register(r'business-ideas', SimpleBusinessIdeaViewSet, basename='business-ideas')
router.register(r'progress-updates', SimpleProgressUpdateViewSet, basename='progress-updates')

# Template analytics functionality
router.register(r'template-analytics', TemplateAnalyticsViewSet, basename='template-analytics')

urlpatterns = [
    path('', include(router.urls)),
]
