import { apiClient } from './apiClient';
import { apiCache, cacheKeys } from '../utils/apiCache';

// Analytics data interfaces
export interface AnalyticsOverview {
  total_users: number;
  total_business_ideas: number;
  total_funding_raised: number;
  active_mentorships: number;
  completed_milestones: number;
  success_rate: number;
}

export interface AnalyticsGrowth {
  user_growth: number;
  idea_growth: number;
  funding_growth: number;
  mentorship_growth: number;
}

export interface AnalyticsCategory {
  name: string;
  count: number;
  percentage: number;
}

export interface AnalyticsActivity {
  type: string;
  description: string;
  timestamp: string;
  user: string;
}

export interface AnalyticsData {
  overview: AnalyticsOverview;
  growth: AnalyticsGrowth;
  categories: AnalyticsCategory[];
  recent_activities: AnalyticsActivity[];
}

export interface UserAnalytics {
  user_id: number;
  total_ideas: number;
  total_funding: number;
  mentorship_sessions: number;
  completed_milestones: number;
  success_rate: number;
  activity_score: number;
}

export interface BusinessIdeaAnalytics {
  idea_id: number;
  views: number;
  likes: number;
  comments: number;
  funding_received: number;
  milestone_progress: number;
  success_probability: number;
}

// Analytics API service
export const analyticsAPI = {
  // Get general analytics data
  async getAnalytics(timeRange: string = '30d'): Promise<AnalyticsData> {
    return apiCache.get(
      cacheKeys.analytics(timeRange),
      async () => {
        try {
          const response = await apiClient.get(`/analytics/overview/?time_range=${timeRange}`);
          return response.data;
        } catch (error) {
      console.error('Error fetching analytics:', error);
      // Return minimal error state instead of mock data
      return {
        overview: {
          total_users: 0,
          total_business_ideas: 0,
          total_funding_raised: 0,
          active_mentorships: 0,
          completed_milestones: 0,
          success_rate: 0
        },
        growth: {
          user_growth: 0,
          idea_growth: 0,
          funding_growth: 0,
          mentorship_growth: 0
        },
        categories: [],
        recent_activities: [],
        error: 'Failed to load analytics data',
        timestamp: new Date().toISOString()
      };
        }
      },
      5 // Cache analytics for 5 minutes
    );
  },

  // Get user-specific analytics
  async getUserAnalytics(userId: number, timeRange: string = '30d'): Promise<UserAnalytics> {
    try {
      const response = await apiClient.get(`/analytics/users/${userId}/?time_range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user analytics:', error);
      // Return error state instead of mock data
      return {
        total_users: 0,
        new_users: 0,
        active_users: 0,
        user_growth_rate: 0,
        retention_rate: 0,
        user_demographics: {
          age_groups: []
        },
        error: 'Failed to load user analytics',
        timestamp: new Date().toISOString()
      };
    }
  },

  // Get real-time analytics
  async getRealTimeAnalytics(): Promise<any> {
    try {
      const response = await apiClient.get('/analytics/real-time/');
      return response.data;
    } catch (error) {
      console.error('Error fetching real-time analytics:', error);
      // Return empty data structure on error
      return {
        activeUsers: 0,
        pageViews: 0,
        sessionDuration: 0,
        bounceRate: 0,
        conversionRate: 0,
        topPages: [],
        userFlow: [],
        deviceBreakdown: { desktop: 0, mobile: 0, tablet: 0 },
        geographicData: [],
        realTimeEvents: []
      };
    }
  },

  // Get dashboard analytics
  async getDashboardAnalytics(): Promise<any> {
    try {
      const response = await apiClient.get('/analytics/dashboard/');
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard analytics:', error);
      return {
        total_users: 150,
        active_users: 120,
        total_posts: 45,
        total_events: 12,
        growth_rate: 15.5,
        engagement_rate: 78.2
      };
    }
  },

  // Get business idea analytics
  async getBusinessIdeaAnalytics(ideaId: number, timeRange: string = '30d'): Promise<BusinessIdeaAnalytics> {
    try {
      const response = await apiClient.get(`/analytics/business-ideas/${ideaId}/?time_range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching business idea analytics:', error);
      // Return error state instead of mock data
      return {
        views: 0,
        likes: 0,
        comments: 0,
        funding_interest: 0,
        mentor_interest: 0,
        growth_trend: [],
        engagement_score: 0,
        error: 'Failed to load business idea analytics',
        timestamp: new Date().toISOString()
      };
    }
  },

  // Get platform metrics
  async getPlatformMetrics(timeRange: string = '30d'): Promise<any> {
    return apiCache.get(
      cacheKeys.platformMetrics(timeRange),
      async () => {
        try {
          const response = await apiClient.get(`/analytics/platform/?time_range=${timeRange}`);
          return response.data;
        } catch (error) {
      console.error('Error fetching platform metrics:', error);
      // Return mock data if API fails
      return {
        data: {
          total_users: 150,
          active_users: 120,
          total_posts: 45,
          total_events: 12,
          growth_rate: 15.5,
          engagement_rate: 78.2
        }
      };
        }
      },
      5 // Cache platform metrics for 5 minutes
    );
  },

  // Get funding analytics
  async getFundingAnalytics(timeRange: string = '30d'): Promise<any> {
    try {
      const response = await apiClient.get(`/analytics/funding/?time_range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching funding analytics:', error);
      // Return mock data if API fails
      return {
        total_funding: 2500000,
        funded_projects: 15,
        average_funding: 166667,
        funding_growth: 25.5,
        top_categories: [
          { name: 'Technology', amount: 1000000 },
          { name: 'Healthcare', amount: 750000 },
          { name: 'Education', amount: 500000 }
        ]
      };
    }
  },

  // Get mentorship analytics
  async getMentorshipAnalytics(timeRange: string = '30d'): Promise<any> {
    try {
      const response = await apiClient.get(`/analytics/mentorship/?time_range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching mentorship analytics:', error);
      // Return mock data if API fails
      return {
        total_mentors: 45,
        active_mentorships: 32,
        completed_sessions: 128,
        average_rating: 4.7,
        success_rate: 85.5
      };
    }
  },

  // Get category analytics
  async getCategoryAnalytics(timeRange: string = '30d'): Promise<AnalyticsCategory[]> {
    try {
      const response = await apiClient.get(`/analytics/categories/?time_range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching category analytics:', error);
      // Return mock data if API fails
      return [
        { name: 'Technology', count: 25, growth: 15.5 },
        { name: 'Business', count: 18, growth: 12.3 },
        { name: 'Education', count: 12, growth: 8.7 },
        { name: 'Healthcare', count: 10, growth: 22.1 }
      ];
    }
  },

  // Get activity feed
  async getActivityFeed(limit: number = 10, timeRange: string = '30d'): Promise<AnalyticsActivity[]> {
    try {
      const response = await apiClient.get(`/analytics/activities/?limit=${limit}&time_range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching activity feed:', error);
      // Return mock data if API fails
      return [
        {
          id: 1,
          type: 'user_registration',
          description: 'New user registered',
          timestamp: new Date().toISOString(),
          user: 'John Doe'
        },
        {
          id: 2,
          type: 'business_idea_created',
          description: 'New business idea submitted',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          user: 'Jane Smith'
        }
      ];
    }
  },

  // Export analytics data
  async exportAnalytics(format: 'csv' | 'xlsx' | 'pdf', timeRange: string = '30d'): Promise<Blob> {
    try {
      const response = await apiClient.get(`/analytics/export/?format=${format}&time_range=${timeRange}`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting analytics:', error);
      // Return empty blob if API fails
      return new Blob(['Export failed'], { type: 'text/plain' });
    }
  }
};

export default analyticsAPI;
