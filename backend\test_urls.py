#!/usr/bin/env python
import os
import sys
import django

# Set the settings module
os.environ['DJANGO_SETTINGS_MODULE'] = 'yasmeen_ai.settings'

# Setup Django
django.setup()

# Import Django components
from django.conf import settings
from django.core.management import execute_from_command_line
from django.urls import get_resolver

print(f"Settings module: {settings.SETTINGS_MODULE}")
print(f"ROOT_URLCONF: {settings.ROOT_URLCONF}")

# Get the URL resolver
resolver = get_resolver()
print(f"Resolver URL conf: {resolver.urlconf_name}")

# Print all URL patterns
print("URL patterns:")
for pattern in resolver.url_patterns:
    print(f"  {pattern}")

# Try to resolve the auth URL
from django.urls import resolve, reverse
try:
    resolved = resolve('/api/auth/')
    print(f"Resolved /api/auth/: {resolved}")
except Exception as e:
    print(f"Failed to resolve /api/auth/: {e}")

# Check if we can import the auth URLs
try:
    from users.auth_urls import urlpatterns as auth_patterns
    print(f"Auth URL patterns: {auth_patterns}")
except Exception as e:
    print(f"Failed to import auth URLs: {e}")